import psycopg2
import pandas as pd
from datetime import datetime

try:
    with psycopg2.connect(
        host="ol7985ua81.b49qsab0at.tsdb.cloud.timescale.com",
        port=31277,
        dbname="tsdb",
        user="tsdbadmin",
        password="rpltd7b7w9gnka4s",
        options='-c search_path=public'
    ) as conn:
        
        # 修改SQL，加上macid过滤
        SQL = (
            "SELECT * FROM ess_string_hon "
            "WHERE ts BETWEEN %s AND %s "
            "AND macid = %s "
            "ORDER BY ts"
        )
        
        start_date = datetime(2025, 1, 1)
        end_date = datetime(2025, 1, 10)
        target_macid = "8C-1F-64-C5-12-52"
        max_rows = 11400
        
        # 先检查这个macid在指定时间范围内有多少数据
        with conn.cursor() as cur:
            cur.execute(
                "SELECT COUNT(*) FROM ess_string_hon WHERE ts BETWEEN %s AND %s AND macid = %s", 
                (start_date, end_date, target_macid)
            )
            total_count = cur.fetchone()[0]
            print(f"MAC ID {target_macid} 在时间范围内总共有 {total_count} 行数据")
            
            if total_count == 0:
                print("⚠️  这个MAC ID在指定时间范围内没有数据")
                # 看看这个macid总共有多少数据
                cur.execute("SELECT COUNT(*) FROM ess_string_hon WHERE macid = %s", (target_macid,))
                total_macid_count = cur.fetchone()[0]
                print(f"但是这个MAC ID总共有 {total_macid_count} 行数据")
                
                if total_macid_count > 0:
                    # 看看时间范围
                    cur.execute("SELECT MIN(ts), MAX(ts) FROM ess_string_hon WHERE macid = %s", (target_macid,))
                    min_ts, max_ts = cur.fetchone()
                    print(f"时间范围: {min_ts} 到 {max_ts}")
                exit()
            
            # 执行主查询
            cur.execute(SQL, (start_date, end_date, target_macid))
            columns = [desc[0] for desc in cur.description]
            rows = []
            
            while len(rows) < max_rows:
                batch = cur.fetchmany(min(1000, max_rows - len(rows)))
                if not batch:
                    break
                rows.extend(batch)
                print(f"已获取 {len(rows)} 行...")
            
            df = pd.DataFrame(rows, columns=columns)
            
        # 用macid命名文件，更清楚
        filename = f"continuous_2024H1_MAC_{target_macid.replace('-', '_')}.csv"
        df.to_csv(filename, index=False)
        print(f"✅ 导出完成，共 {len(df)} 行，文件名 {filename}")

except psycopg2.Error as e:
    print(f"❌ 数据库错误: {e}")
except Exception as e:
    print(f"❌ 其他错误: {e}")
