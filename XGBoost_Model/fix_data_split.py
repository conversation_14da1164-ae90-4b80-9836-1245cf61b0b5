#!/usr/bin/env python3
"""
修复数据划分问题 - 避免时序泄露
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
import os

def fix_data_split():
    """重新划分训练和验证集，避免时序泄露"""
    
    print("🔧 修复数据划分问题...")
    
    # 1. 加载所有数据
    print("📁 加载原始数据...")
    
    # 加载窗口数据
    train_windows = pd.read_csv('data/processed_data/train_windows.csv')
    val_windows = pd.read_csv('data/processed_data/val_windows.csv')
    
    # 加载TSFresh特征
    train_tsfresh = pd.read_csv('data/processed_data/train_tsfresh_features.csv', index_col=0)
    val_tsfresh = pd.read_csv('data/processed_data/val_tsfresh_features.csv', index_col=0)
    
    # 加载标签
    train_labels = pd.read_csv('data/processed_data/train_feature_labels.csv')
    val_labels = pd.read_csv('data/processed_data/val_feature_labels.csv')
    
    print(f"   原训练集: {len(train_labels)} 样本")
    print(f"   原验证集: {len(val_labels)} 样本")
    
    # 2. 合并所有数据
    print("🔗 合并所有数据...")
    
    # 合并窗口数据
    all_windows = pd.concat([train_windows, val_windows], ignore_index=True)
    
    # 合并TSFresh特征
    train_tsfresh.reset_index(drop=True, inplace=True)
    val_tsfresh.reset_index(drop=True, inplace=True)
    all_tsfresh = pd.concat([train_tsfresh, val_tsfresh], ignore_index=True)
    
    # 合并标签
    all_labels = pd.concat([train_labels, val_labels], ignore_index=True)
    
    print(f"   合并后总样本数: {len(all_labels)}")
    print(f"   异常样本: {(all_labels['label'] == 1).sum()}")
    print(f"   正常样本: {(all_labels['label'] == 0).sum()}")
    
    # 3. 按窗口ID重新随机划分
    print("🎲 重新随机划分数据...")
    
    # 获取唯一的窗口ID和对应标签
    window_labels = all_labels.drop_duplicates('window_id').copy()
    
    # 分层随机划分，保持类别比例
    train_windows_ids, val_windows_ids = train_test_split(
        window_labels['window_id'].values,
        test_size=0.2,  # 20%作为验证集
        stratify=window_labels['label'].values,
        random_state=42
    )
    
    print(f"   新训练集窗口数: {len(train_windows_ids)}")
    print(f"   新验证集窗口数: {len(val_windows_ids)}")
    
    # 4. 重新生成训练和验证数据
    print("📊 生成新的训练和验证集...")
    
    # 生成新的标签文件
    new_train_labels = all_labels[all_labels['window_id'].isin(train_windows_ids)].copy()
    new_val_labels = all_labels[all_labels['window_id'].isin(val_windows_ids)].copy()
    
    # 重新排序并重置索引
    new_train_labels = new_train_labels.sort_values('window_id').reset_index(drop=True)
    new_val_labels = new_val_labels.sort_values('window_id').reset_index(drop=True)
    
    # 生成新的窗口数据
    new_train_windows = all_windows[all_windows['window_id'].isin(train_windows_ids)].copy()
    new_val_windows = all_windows[all_windows['window_id'].isin(val_windows_ids)].copy()
    
    # 生成新的TSFresh特征
    train_mask = all_labels['window_id'].isin(train_windows_ids)
    val_mask = all_labels['window_id'].isin(val_windows_ids)
    
    new_train_tsfresh = all_tsfresh[train_mask].reset_index(drop=True)
    new_val_tsfresh = all_tsfresh[val_mask].reset_index(drop=True)
    
    # 5. 保存新的数据文件
    print("💾 保存修复后的数据...")
    
    # 备份原文件
    backup_dir = 'data/processed_data/backup'
    os.makedirs(backup_dir, exist_ok=True)
    
    files_to_backup = [
        'train_windows.csv', 'val_windows.csv',
        'train_tsfresh_features.csv', 'val_tsfresh_features.csv', 
        'train_feature_labels.csv', 'val_feature_labels.csv'
    ]
    
    for file in files_to_backup:
        src = f'data/processed_data/{file}'
        dst = f'{backup_dir}/{file}'
        if os.path.exists(src):
            os.system(f'cp "{src}" "{dst}"')
    
    print(f"   原文件已备份到: {backup_dir}")
    
    # 保存新文件
    new_train_windows.to_csv('data/processed_data/train_windows.csv', index=False)
    new_val_windows.to_csv('data/processed_data/val_windows.csv', index=False)
    
    new_train_tsfresh.to_csv('data/processed_data/train_tsfresh_features.csv')
    new_val_tsfresh.to_csv('data/processed_data/val_tsfresh_features.csv')
    
    new_train_labels.to_csv('data/processed_data/train_feature_labels.csv', index=False)
    new_val_labels.to_csv('data/processed_data/val_feature_labels.csv', index=False)
    
    # 6. 验证新数据
    print("✅ 验证修复结果...")
    
    print(f"   新训练集: {len(new_train_labels)} 样本")
    print(f"     异常: {(new_train_labels['label'] == 1).sum()}")
    print(f"     正常: {(new_train_labels['label'] == 0).sum()}")
    print(f"     异常率: {(new_train_labels['label'] == 1).mean()*100:.1f}%")
    
    print(f"   新验证集: {len(new_val_labels)} 样本")
    print(f"     异常: {(new_val_labels['label'] == 1).sum()}")
    print(f"     正常: {(new_val_labels['label'] == 0).sum()}")
    print(f"     异常率: {(new_val_labels['label'] == 1).mean()*100:.1f}%")
    
    print("\n🎉 数据划分修复完成！")
    print("现在可以重新训练模型，应该会得到更好的Precision。")

if __name__ == "__main__":
    fix_data_split()
