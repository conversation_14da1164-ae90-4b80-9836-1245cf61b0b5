#!/usr/bin/env python3
"""
2_train_xgboost_model.py (最终修复版)

训练XGBoost模型进行电芯不平衡异常检测
修复了所有变量作用域和兼容性问题

输入：data/processed_data/train_tsfresh_features.csv + val_tsfresh_features.csv
输出：models/xgboost_model.pkl + 完整的评估报告
"""

import pandas as pd
import numpy as np
import json
import pickle
import os
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 机器学习相关
import xgboost as xgb
from sklearn.metrics import (
    classification_report, confusion_matrix, 
    roc_auc_score, roc_curve, precision_recall_curve,
    f1_score, precision_score, recall_score, average_precision_score
)

# 可视化
import matplotlib.pyplot as plt
import seaborn as sns
plt.rcParams['figure.figsize'] = (10, 6)
plt.rcParams['font.size'] = 12

class XGBoostAnomalyDetector:
    def __init__(self):
        self.model = None
        self.best_threshold = 0.5
        self.feature_names = None
        self.training_history = {}
        
    def step_indicator(self, step_num, total_steps, description):
        """显示当前步骤进度"""
        progress = f"[{step_num}/{total_steps}]"
        print(f"\n{'='*60}")
        print(f"🎯 步骤 {progress} {description}")
        print(f"{'='*60}")
    
    def load_and_validate_data(self):
        """加载训练和验证数据"""
        self.step_indicator(1, 8, "数据加载与预处理")

        print("📁 加载特征和标签数据...")

        # 加载原始特征数据（包含原始特征）
        train_windows_path = "data/processed_data/train_windows.csv"
        val_windows_path = "data/processed_data/val_windows.csv"

        # 加载TSFresh特征数据
        train_tsfresh_path = "data/processed_data/train_tsfresh_features.csv"
        val_tsfresh_path = "data/processed_data/val_tsfresh_features.csv"

        # 加载标签数据
        train_labels_path = "data/processed_data/train_feature_labels.csv"
        val_labels_path = "data/processed_data/val_feature_labels.csv"

        # 检查文件存在性
        required_files = [train_windows_path, val_windows_path, train_tsfresh_path, val_tsfresh_path, train_labels_path, val_labels_path]
        for path in required_files:
            if not os.path.exists(path):
                raise FileNotFoundError(f"找不到文件: {path}")

        # 加载原始特征数据
        print("   加载原始特征数据...")
        train_windows = pd.read_csv(train_windows_path)
        val_windows = pd.read_csv(val_windows_path)

        # 加载TSFresh特征数据
        print("   加载TSFresh特征数据...")
        train_tsfresh = pd.read_csv(train_tsfresh_path, index_col=0)
        val_tsfresh = pd.read_csv(val_tsfresh_path, index_col=0)

        # 加载标签数据
        y_train = pd.read_csv(train_labels_path)
        y_val = pd.read_csv(val_labels_path)
        
        # 合并原始特征和TSFresh特征
        print("� 合并原始特征和TSFresh特征...")

        # 定义核心原始特征（与特征提取脚本保持一致）
        # 明确指定数值特征，避免包含字符串列
        CORE_FEATURES = [
            'cellvdelta',        # 电芯电压差（最重要）
            'averagecellv',      # 平均电芯电压
            'hcellv',           # 最高电芯电压
            'lcellv',           # 最低电芯电压
            'systemvolt',       # 系统电压
            'totalcurrenta',    # 总电流
            'soc',              # SOC
            'htempc',           # 最高温度
            'ltempc'            # 最低温度
            # 注意：移除 balancestatus，因为它可能包含非数值数据
        ]

        # 从原始数据中按窗口聚合特征
        print("   聚合训练集原始特征...")

        # 确保只选择数值列进行聚合
        numeric_core_features = []
        for feature in CORE_FEATURES:
            if feature in train_windows.columns:
                # 检查是否为数值类型
                if pd.api.types.is_numeric_dtype(train_windows[feature]):
                    numeric_core_features.append(feature)
                else:
                    print(f"   跳过非数值特征: {feature} (类型: {train_windows[feature].dtype})")

        print(f"   使用数值特征: {numeric_core_features}")

        train_agg_features = train_windows.groupby('window_id')[numeric_core_features].agg([
            'mean', 'std', 'min', 'max', 'median'
        ]).round(6)

        # 展平多级列名
        train_agg_features.columns = ['_'.join(col).strip() for col in train_agg_features.columns.values]
        train_agg_features.reset_index(drop=True, inplace=True)

        print("   聚合验证集原始特征...")
        val_agg_features = val_windows.groupby('window_id')[numeric_core_features].agg([
            'mean', 'std', 'min', 'max', 'median'
        ]).round(6)

        # 展平多级列名
        val_agg_features.columns = ['_'.join(col).strip() for col in val_agg_features.columns.values]
        val_agg_features.reset_index(drop=True, inplace=True)

        # 重置TSFresh特征的索引，确保能够正确合并
        train_tsfresh.reset_index(drop=True, inplace=True)
        val_tsfresh.reset_index(drop=True, inplace=True)

        # 合并聚合的原始特征和TSFresh特征
        X_train = pd.concat([train_agg_features, train_tsfresh], axis=1)
        X_val = pd.concat([val_agg_features, val_tsfresh], axis=1)

        print(f"✅ 数据加载完成:")
        print(f"   训练集聚合原始特征: {train_agg_features.shape}")
        print(f"   训练集TSFresh特征: {train_tsfresh.shape}")
        print(f"   训练集合并特征: {X_train.shape}")
        print(f"   验证集合并特征: {X_val.shape}")
        print(f"   训练标签: {len(y_train)}")
        print(f"   验证标签: {len(y_val)}")

        # 数据验证
        print("\n🔍 数据质量检查...")

        # 检查标签对齐
        if len(X_train) != len(y_train):
            raise ValueError(f"训练集特征({len(X_train)})和标签({len(y_train)})数量不匹配")
        if len(X_val) != len(y_val):
            raise ValueError(f"验证集特征({len(X_val)})和标签({len(y_val)})数量不匹配")

        # 分析特征类型
        print("🧹 分析特征类型...")
        all_features = X_train.columns.tolist()
        # 聚合的原始特征包含统计后缀（_mean, _std等）
        aggregated_original_features = [col for col in all_features if any(core_feat in col for core_feat in CORE_FEATURES) and '__' not in col]
        tsfresh_features = [col for col in all_features if '__' in col]  # tsfresh特征包含__

        print(f"   总特征数: {len(all_features)}")
        print(f"   聚合原始特征数: {len(aggregated_original_features)}")
        print(f"   TSFresh特征数: {len(tsfresh_features)}")

        if len(aggregated_original_features) > 0:
            print(f"   聚合原始特征示例: {aggregated_original_features[:5]}")
        if len(tsfresh_features) > 0:
            print(f"   TSFresh特征示例: {tsfresh_features[:3]}...")

        # 保存特征名称（用于后续分析）
        self.feature_names = all_features
        
        # 提取标签
        y_train = y_train['label'].values
        y_val = y_val['label'].values
        
        # 检查缺失值
        train_missing = X_train.isnull().sum().sum()
        val_missing = X_val.isnull().sum().sum()
        
        if train_missing > 0 or val_missing > 0:
            print(f"⚠️  发现缺失值: 训练集{train_missing}, 验证集{val_missing}")
            X_train = X_train.fillna(X_train.mean())
            X_val = X_val.fillna(X_train.mean())  # 用训练集均值填充
            print("✅ 已用训练集均值填充缺失值")
        
        # 显示类别分布
        train_pos = (y_train == 1).sum()
        train_neg = (y_train == 0).sum()
        val_pos = (y_val == 1).sum()
        val_neg = (y_val == 0).sum()
        
        print(f"\n📊 类别分布:")
        print(f"   训练集: {train_pos}异常 + {train_neg}正常 ({train_pos/(train_pos+train_neg)*100:.1f}%异常率)")
        print(f"   验证集: {val_pos}异常 + {val_neg}正常 ({val_pos/(val_pos+val_neg)*100:.1f}%异常率)")
        
        return X_train, X_val, y_train, y_val
    
    def configure_class_balance(self, y_train):
        """配置类别不平衡处理参数"""
        self.step_indicator(2, 8, "类别不平衡处理配置")

        pos_samples = (y_train == 1).sum()
        neg_samples = (y_train == 0).sum()
        raw_scale_pos_weight = neg_samples / pos_samples

        # 减少过度偏向异常类，使用平方根缓解不平衡
        # 这样可以提高precision，减少假阳性
        adjusted_scale_pos_weight = np.sqrt(raw_scale_pos_weight)

        print(f"⚖️  类别不平衡分析:")
        print(f"   正常样本: {neg_samples}")
        print(f"   异常样本: {pos_samples}")
        print(f"   不平衡比例: {neg_samples/pos_samples:.1f}:1")
        print(f"   原始scale_pos_weight: {raw_scale_pos_weight:.2f}")
        print(f"   调整后scale_pos_weight: {adjusted_scale_pos_weight:.2f} (减少假阳性)")

        return float(adjusted_scale_pos_weight)
    
    def train_baseline_model(self, X_train, X_val, y_train, y_val, scale_pos_weight):
        """训练基础模型进行快速验证"""
        self.step_indicator(3, 8, "基础模型训练与快速验证")
        
        print("🏗️  构建基础XGBoost模型...")
        print(f"XGBoost版本: {xgb.__version__}")
        
        # 使用最简化的参数设置
        base_params = {
            'n_estimators': 100,
            'max_depth': 6,
            'learning_rate': 0.1,
            'subsample': 0.8,
            'colsample_bytree': 0.8,
            'scale_pos_weight': scale_pos_weight,
            'objective': 'binary:logistic',
            'random_state': 42,
            'n_jobs': -1
        }
        
        print(f"   基础参数: {base_params}")
        
        # 创建并训练基础模型 - 使用最简化的API
        baseline_model = xgb.XGBClassifier(**base_params)
        
        print("🚀 开始训练基础模型...")
        
        # 最简化的fit调用，只传递必要参数
        baseline_model.fit(X_train, y_train)
        
        print("✅ 基础模型训练完成")
        
        # 快速评估
        y_pred = baseline_model.predict(X_val)
        y_pred_proba = baseline_model.predict_proba(X_val)[:, 1]
        
        baseline_f1 = f1_score(y_val, y_pred)
        baseline_auc = roc_auc_score(y_val, y_pred_proba)
        baseline_precision = precision_score(y_val, y_pred)
        baseline_recall = recall_score(y_val, y_pred)
        
        print(f"\n📊 基础模型性能:")
        print(f"   F1-Score: {baseline_f1:.3f}")
        print(f"   AUC-ROC: {baseline_auc:.3f}")
        print(f"   Precision: {baseline_precision:.3f}")
        print(f"   Recall: {baseline_recall:.3f}")
        
        # 保存基础性能作为基准
        self.training_history['baseline'] = {
            'f1_score': baseline_f1,
            'auc_roc': baseline_auc,
            'precision': baseline_precision,
            'recall': baseline_recall
        }
        
        return baseline_model, base_params
    
    def hyperparameter_tuning(self, X_train, X_val, y_train, y_val, base_params):
        """进行超参数调优"""
        self.step_indicator(4, 8, "超参数调优")
        
        print("🎛️  开始超参数调优...")
        print("使用手动网格搜索确保最大兼容性")
        
        # 直接在验证集上进行手动网格搜索
        print("\n🔍 手动超参数搜索...")
        
        best_f1 = 0
        best_params = {}
        
        # 定义搜索空间（减少参数组合以加快速度）
        param_grid = {
            'n_estimators': [50, 100, 200],
            'max_depth': [4, 6, 8],
            'learning_rate': [0.1, 0.15, 0.2],
        }
        
        total_combinations = len(param_grid['n_estimators']) * len(param_grid['max_depth']) * len(param_grid['learning_rate'])
        print(f"   搜索空间: {total_combinations} 种参数组合")
        
        current_combination = 0
        
        for n_est in param_grid['n_estimators']:
            for max_d in param_grid['max_depth']:
                for lr in param_grid['learning_rate']:
                    current_combination += 1
                    
                    # 创建测试参数
                    test_params = base_params.copy()
                    test_params.update({
                        'n_estimators': n_est,
                        'max_depth': max_d,
                        'learning_rate': lr
                    })
                    
                    # 训练临时模型
                    temp_model = xgb.XGBClassifier(**test_params)
                    temp_model.fit(X_train, y_train)
                    
                    # 在验证集上评估
                    y_pred = temp_model.predict(X_val)
                    f1 = f1_score(y_val, y_pred)
                    
                    print(f"   [{current_combination:2d}/{total_combinations}] n_est={n_est}, max_d={max_d}, lr={lr:.2f} → F1={f1:.3f}")
                    
                    # 更新最佳参数
                    if f1 > best_f1:
                        best_f1 = f1
                        best_params = {
                            'n_estimators': n_est,
                            'max_depth': max_d,
                            'learning_rate': lr
                        }
                        print(f"   ⭐ 新的最佳参数! F1={f1:.3f}")
        
        print(f"\n✅ 超参数搜索完成:")
        print(f"   最佳F1分数: {best_f1:.3f}")
        print(f"   最佳参数: {best_params}")
        
        # 创建最佳模型
        final_params = {**base_params, **best_params}
        best_model = xgb.XGBClassifier(**final_params)
        
        # 保存调优历史
        self.training_history['hyperparameter_tuning'] = {
            'manual_search_best_score': best_f1,
            'manual_search_best_params': best_params,
            'total_combinations_tested': total_combinations
        }
        
        return best_model, best_params
    
    def train_final_model(self, X_train, X_val, y_train, y_val, best_params, base_params):
        """使用最优参数训练最终模型"""
        self.step_indicator(5, 8, "最终模型训练")
        
        print("🏆 使用最优参数训练最终模型...")
        
        # 合并参数
        final_params = {**base_params, **best_params}
        print(f"   最终参数: {final_params}")
        
        # 训练最终模型
        final_model = xgb.XGBClassifier(**final_params)
        
        print("🚀 开始训练最终模型...")
        final_model.fit(X_train, y_train)
        
        print("✅ 最终模型训练完成")
        
        # 在训练集和验证集上评估
        train_pred_proba = final_model.predict_proba(X_train)[:, 1]
        val_pred_proba = final_model.predict_proba(X_val)[:, 1]
        
        train_auc = roc_auc_score(y_train, train_pred_proba)
        val_auc = roc_auc_score(y_val, val_pred_proba)
        
        print(f"   训练集AUC: {train_auc:.3f}")
        print(f"   验证集AUC: {val_auc:.3f}")
        
        self.model = final_model
        return final_model
    
    def optimize_threshold_and_evaluate(self, X_val, y_val):
        """优化分类阈值并进行全面评估"""
        self.step_indicator(6, 8, "阈值优化与模型评估")

        print("🎯 优化分类阈值...")

        # 获取预测概率
        y_pred_proba = self.model.predict_proba(X_val)[:, 1]

        # 在不同阈值下评估多个指标
        thresholds = np.arange(0.1, 0.9, 0.05)
        f1_scores = []
        precision_scores = []
        recall_scores = []

        print("   阈值优化详情:")
        print("   阈值    F1     Precision  Recall")
        print("   " + "-" * 35)

        for threshold in thresholds:
            y_pred_thresh = (y_pred_proba >= threshold).astype(int)
            f1 = f1_score(y_val, y_pred_thresh)
            precision = precision_score(y_val, y_pred_thresh, zero_division=0)
            recall = recall_score(y_val, y_pred_thresh, zero_division=0)

            f1_scores.append(f1)
            precision_scores.append(precision)
            recall_scores.append(recall)

            print(f"   {threshold:.2f}    {f1:.3f}  {precision:.3f}     {recall:.3f}")

        # 找到最佳阈值 - 平衡F1和Precision
        # 使用加权评分：60% F1 + 40% Precision
        weighted_scores = [0.6 * f1 + 0.4 * prec for f1, prec in zip(f1_scores, precision_scores)]
        best_threshold_idx = np.argmax(weighted_scores)
        self.best_threshold = thresholds[best_threshold_idx]
        best_f1 = f1_scores[best_threshold_idx]
        best_precision = precision_scores[best_threshold_idx]

        print(f"\n   最佳阈值: {self.best_threshold:.3f} (平衡F1和Precision)")
        print(f"   对应F1分数: {best_f1:.3f}")
        print(f"   对应Precision: {best_precision:.3f}")
        
        # 使用最佳阈值进行预测
        y_pred_final = (y_pred_proba >= self.best_threshold).astype(int)
        
        # 全面评估
        print("\n📊 最终模型性能评估:")
        
        # 基础指标
        final_f1 = f1_score(y_val, y_pred_final)
        final_precision = precision_score(y_val, y_pred_final)
        final_recall = recall_score(y_val, y_pred_final)
        final_auc_roc = roc_auc_score(y_val, y_pred_proba)
        final_auc_pr = average_precision_score(y_val, y_pred_proba)
        
        print(f"   F1-Score: {final_f1:.3f}")
        print(f"   Precision: {final_precision:.3f}")
        print(f"   Recall: {final_recall:.3f}")
        print(f"   AUC-ROC: {final_auc_roc:.3f}")
        print(f"   AUC-PR: {final_auc_pr:.3f}")
        
        # 混淆矩阵
        cm = confusion_matrix(y_val, y_pred_final)
        print(f"\n📈 混淆矩阵:")
        print(f"   TN: {cm[0,0]:2d} | FP: {cm[0,1]:2d}")
        print(f"   FN: {cm[1,0]:2d} | TP: {cm[1,1]:2d}")
        
        # 分类报告
        print(f"\n📋 详细分类报告:")
        print(classification_report(y_val, y_pred_final, target_names=['正常', '异常']))
        
        # 保存评估结果
        self.training_history['final_evaluation'] = {
            'best_threshold': self.best_threshold,
            'f1_score': final_f1,
            'precision': final_precision,
            'recall': final_recall,
            'auc_roc': final_auc_roc,
            'auc_pr': final_auc_pr,
            'confusion_matrix': cm.tolist(),
            'threshold_f1_scores': list(zip(thresholds.tolist(), f1_scores))
        }
        
        return y_pred_final, y_pred_proba
    
    def analyze_feature_importance(self):
        """分析特征重要性"""
        self.step_indicator(7, 8, "特征重要性分析与可视化")
        
        print("🔍 分析特征重要性...")
        
        # 获取特征重要性
        importance = self.model.feature_importances_
        feature_importance_df = pd.DataFrame({
            'feature': self.feature_names,
            'importance': importance
        }).sort_values('importance', ascending=False)
        
        # 分类特征类型
        feature_importance_df['feature_type'] = feature_importance_df['feature'].apply(
            lambda x: 'Original' if '__' not in x else 'TSFresh'
        )
        
        print(f"\n📊 特征类型统计:")
        feature_type_stats = feature_importance_df.groupby('feature_type').agg({
            'importance': ['count', 'sum', 'mean']
        }).round(4)
        print(feature_type_stats)
        
        print(f"\n📊 前10个重要特征:")
        for i, (_, row) in enumerate(feature_importance_df.head(10).iterrows()):
            feature_type = "原始" if row['feature_type'] == 'Original' else "时序"
            print(f"   {i+1:2d}. [{feature_type}] {row['feature']:<45} {row['importance']:.4f}")
        
        # 分别显示原始特征和TSFresh特征的Top5
        original_features = feature_importance_df[feature_importance_df['feature_type'] == 'Original'].head(5)
        tsfresh_features = feature_importance_df[feature_importance_df['feature_type'] == 'TSFresh'].head(5)
        
        if len(original_features) > 0:
            print(f"\n📊 最重要的原始特征:")
            for i, (_, row) in enumerate(original_features.iterrows()):
                print(f"   {i+1}. {row['feature']:<20} {row['importance']:.4f}")
        
        if len(tsfresh_features) > 0:
            print(f"\n📊 最重要的TSFresh特征:")
            for i, (_, row) in enumerate(tsfresh_features.iterrows()):
                print(f"   {i+1}. {row['feature']:<45} {row['importance']:.4f}")
        
        # 保存特征重要性
        os.makedirs('results', exist_ok=True)
        feature_importance_df.to_csv('results/feature_importance.csv', index=False)
        
        # 绘制特征重要性图
        try:
            # 创建两个子图：总体Top15 + 分类对比
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 8))
            
            # 左图：Top15特征
            top_features = feature_importance_df.head(15)
            colors = ['#1f77b4' if ft == 'Original' else '#ff7f0e' for ft in top_features['feature_type']]
            
            ax1.barh(range(len(top_features)), top_features['importance'], color=colors)
            ax1.set_yticks(range(len(top_features)))
            ax1.set_yticklabels(top_features['feature'])
            ax1.set_xlabel('特征重要性')
            ax1.set_title('特征重要性排序 (Top 15)')
            ax1.invert_yaxis()
            
            # 添加图例
            from matplotlib.patches import Patch
            legend_elements = [Patch(facecolor='#1f77b4', label='原始特征'),
                             Patch(facecolor='#ff7f0e', label='TSFresh特征')]
            ax1.legend(handles=legend_elements)
            
            # 右图：特征类型重要性对比
            type_importance = feature_importance_df.groupby('feature_type')['importance'].agg(['sum', 'mean', 'count'])
            
            x_pos = [0, 1]
            ax2.bar(x_pos, type_importance['sum'], color=['#1f77b4', '#ff7f0e'], alpha=0.7)
            ax2.set_xticks(x_pos)
            ax2.set_xticklabels(['原始特征', 'TSFresh特征'])
            ax2.set_ylabel('重要性总和')
            ax2.set_title('特征类型重要性对比')
            
            # 在柱子上添加数值标签
            for i, v in enumerate(type_importance['sum']):
                ax2.text(i, v + 0.01, f'{v:.3f}\n({type_importance["count"].iloc[i]}个)', 
                        ha='center', va='bottom')
            
            plt.tight_layout()
            plt.savefig('results/feature_importance_analysis.png', dpi=300, bbox_inches='tight')
            plt.close()
            print(f"   特征重要性图表: results/feature_importance_analysis.png")
        except Exception as e:
            print(f"   ⚠️  绘图失败: {e}")
        
        print(f"✅ 特征重要性分析完成")
        print(f"   特征重要性文件: results/feature_importance.csv")
        
        return feature_importance_df
    
    def save_model_and_generate_docs(self, feature_importance_df):
        """保存模型和生成完整文档"""
        self.step_indicator(8, 8, "模型保存与文档生成")
        
        print("💾 保存模型和生成文档...")
        
        # 创建models目录
        os.makedirs('models', exist_ok=True)
        
        # 保存模型
        model_path = 'models/xgboost_model.pkl'
        with open(model_path, 'wb') as f:
            pickle.dump(self.model, f)
        print(f"   模型文件: {model_path}")
        
        # 保存阈值配置
        threshold_config = {
            'best_threshold': self.best_threshold,
            'threshold_optimization_method': 'f1_score_maximization',
            'feature_names': self.feature_names,
            'xgboost_version': xgb.__version__
        }
        
        threshold_path = 'models/threshold_config.json'
        with open(threshold_path, 'w', encoding='utf-8') as f:
            json.dump(threshold_config, f, indent=2, ensure_ascii=False)
        print(f"   阈值配置: {threshold_path}")
        
        # 保存完整的训练元数据
        metadata = {
            'training_timestamp': datetime.now().isoformat(),
            'model_type': 'XGBoost',
            'xgboost_version': xgb.__version__,
            'feature_count': len(self.feature_names),
            'training_history': self.training_history,
            'top_10_features': feature_importance_df.head(10).to_dict('records')
        }
        
        metadata_path = 'models/model_metadata.json'
        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False, default=str)
        print(f"   模型元数据: {metadata_path}")
        
        # 生成使用示例（修复变量作用域问题）
        usage_example = f'''#!/usr/bin/env python3
"""
XGBoost异常检测模型使用示例
兼容XGBoost {xgb.__version__}
"""

import pickle
import pandas as pd
import numpy as np
import json

def load_model():
    """加载训练好的模型和配置"""
    with open('{model_path}', 'rb') as f:
        model = pickle.load(f)
    
    with open('{threshold_path}', 'r') as f:
        config = json.load(f)
    
    return model, config

def predict_anomaly(features_df, model, config):
    """
    预测电芯异常
    
    Args:
        features_df: 包含特征的DataFrame
        model: 训练好的XGBoost模型
        config: 阈值配置
    
    Returns:
        dict: 预测结果
    """
    # 确保特征顺序正确
    expected_features = config['feature_names']
    missing_features = [col for col in expected_features if col not in features_df.columns]
    
    if missing_features:
        raise ValueError(f"缺少特征列: {{missing_features[:5]}}...")
    
    # 选择并排序特征
    X = features_df[expected_features]
    
    # 预测
    probabilities = model.predict_proba(X)[:, 1]
    predictions = (probabilities >= config['best_threshold']).astype(int)
    
    return {{
        'predictions': predictions,
        'probabilities': probabilities,
        'threshold': config['best_threshold'],
        'anomaly_count': predictions.sum(),
        'total_samples': len(predictions),
        'anomaly_rate': predictions.mean()
    }}

# 使用示例
if __name__ == "__main__":
    print("🤖 XGBoost异常检测模型")
    
    # 加载模型
    model, config = load_model()
    print("✅ 模型加载成功")
    print(f"适配XGBoost版本: {{config.get('xgboost_version', 'Unknown')}}")
    print(f"最佳阈值: {{config['best_threshold']:.3f}}")
    print(f"特征数量: {{len(config['feature_names'])}}")
    
    # 示例预测（需要提供实际数据）
    # result = predict_anomaly(your_features_df, model, config)
    # print(f"检测结果: {{result['anomaly_count']}}/{{result['total_samples']}} 异常")
'''
        
        usage_path = 'models/usage_example.py'
        with open(usage_path, 'w', encoding='utf-8') as f:
            f.write(usage_example)
        print(f"   使用示例: {usage_path}")
        
        # 生成简化的性能报告
        self.generate_simple_report()
        
        print(f"\n🎉 模型训练完成！")
        print(f"📁 输出文件:")
        print(f"   ├── {model_path}")
        print(f"   ├── {threshold_path}")
        print(f"   ├── {metadata_path}")
        print(f"   ├── {usage_path}")
        print(f"   ├── results/feature_importance.csv")
        print(f"   └── results/performance_summary.txt")
    
    def generate_simple_report(self):
        """生成简化的性能报告"""
        final_eval = self.training_history.get('final_evaluation', {})
        baseline_eval = self.training_history.get('baseline', {})
        tuning_eval = self.training_history.get('hyperparameter_tuning', {})
        
        report = f"""
电芯不平衡异常检测模型性能报告
=====================================

训练信息:
- 模型类型: XGBoost {xgb.__version__}
- 训练时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 特征数量: {len(self.feature_names)}

性能对比:
                基础模型    最终模型    提升
F1-Score        {baseline_eval.get('f1_score', 0):.3f}      {final_eval.get('f1_score', 0):.3f}      {final_eval.get('f1_score', 0) - baseline_eval.get('f1_score', 0):+.3f}
AUC-ROC         {baseline_eval.get('auc_roc', 0):.3f}      {final_eval.get('auc_roc', 0):.3f}      {final_eval.get('auc_roc', 0) - baseline_eval.get('auc_roc', 0):+.3f}
Precision       {baseline_eval.get('precision', 0):.3f}      {final_eval.get('precision', 0):.3f}      {final_eval.get('precision', 0) - baseline_eval.get('precision', 0):+.3f}
Recall          {baseline_eval.get('recall', 0):.3f}      {final_eval.get('recall', 0):.3f}      {final_eval.get('recall', 0) - baseline_eval.get('recall', 0):+.3f}

超参数调优:
- 测试组合数: {tuning_eval.get('total_combinations_tested', 0)}
- 最佳F1分数: {tuning_eval.get('manual_search_best_score', 0):.3f}

最终配置:
- 分类阈值: {final_eval.get('best_threshold', 0.5):.3f}
- AUC-ROC: {final_eval.get('auc_roc', 0):.3f}
- AUC-PR: {final_eval.get('auc_pr', 0):.3f}

混淆矩阵:
        预测
        正常  异常
实际 正常 {final_eval.get('confusion_matrix', [[0,0],[0,0]])[0][0]:3d}  {final_eval.get('confusion_matrix', [[0,0],[0,0]])[0][1]:3d}
     异常 {final_eval.get('confusion_matrix', [[0,0],[0,0]])[1][0]:3d}  {final_eval.get('confusion_matrix', [[0,0],[0,0]])[1][1]:3d}

使用说明:
1. 使用 models/usage_example.py 进行预测
2. 模型适用于电芯不平衡异常检测
3. 建议定期监控模型性能并重训练
        """
        
        report_path = 'results/performance_summary.txt'
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report.strip())
        print(f"   性能报告: {report_path}")

def main():
    """主训练流程"""
    print("🚀 启动XGBoost异常检测模型训练 (最终修复版)")
    print(f"XGBoost版本: {xgb.__version__}")
    print("="*60)
    
    try:
        # 初始化训练器
        trainer = XGBoostAnomalyDetector()
        
        # 执行8步训练流程
        X_train, X_val, y_train, y_val = trainer.load_and_validate_data()
        scale_pos_weight = trainer.configure_class_balance(y_train)
        baseline_model, base_params = trainer.train_baseline_model(X_train, X_val, y_train, y_val, scale_pos_weight)
        best_model, best_params = trainer.hyperparameter_tuning(X_train, X_val, y_train, y_val, base_params)
        final_model = trainer.train_final_model(X_train, X_val, y_train, y_val, best_params, base_params)
        y_pred, y_pred_proba = trainer.optimize_threshold_and_evaluate(X_val, y_val)
        feature_importance_df = trainer.analyze_feature_importance()
        trainer.save_model_and_generate_docs(feature_importance_df)
        
        # 显示最终总结
        final_eval = trainer.training_history.get('final_evaluation', {})
        baseline_eval = trainer.training_history.get('baseline', {})
        
        print(f"\n✅ 训练流程全部完成！")
        print(f"🎯 模型性能总结:")
        print(f"   基础模型 F1: {baseline_eval.get('f1_score', 0):.3f}")
        print(f"   最终模型 F1: {final_eval.get('f1_score', 0):.3f}")
        print(f"   性能提升: {final_eval.get('f1_score', 0) - baseline_eval.get('f1_score', 0):+.3f}")
        print(f"   AUC-ROC: {final_eval.get('auc_roc', 0):.3f}")
        print(f"   最佳阈值: {final_eval.get('best_threshold', 0.5):.3f}")
        
        print(f"\n🔄 接下来可以:")
        print(f"   1. 查看 results/performance_summary.txt 了解详细性能")
        print(f"   2. 使用 models/usage_example.py 进行预测")
        print(f"   3. 查看 results/feature_importance.csv 分析重要特征")
        print(f"   4. 在测试集上验证模型泛化性能")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 训练过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        exit(1)
