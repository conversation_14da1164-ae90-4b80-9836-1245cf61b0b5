{"training_timestamp": "2025-05-31T20:12:19.211538", "model_type": "XGBoost", "xgboost_version": "3.0.2", "feature_count": 114, "training_history": {"baseline": {"f1_score": 0.6666666666666666, "auc_roc": 0.8333333333333334, "precision": 0.5, "recall": 1.0}, "hyperparameter_tuning": {"manual_search_best_score": 0.6666666666666666, "manual_search_best_params": {"n_estimators": 50, "max_depth": 4, "learning_rate": 0.1}, "total_combinations_tested": 27}, "final_evaluation": {"best_threshold": 0.1, "f1_score": 0.6666666666666666, "precision": 0.5, "recall": 1.0, "auc_roc": 0.8333333333333334, "auc_pr": 0.5, "confusion_matrix": [[20, 10], [0, 10]], "threshold_f1_scores": [[0.1, 0.6666666666666666], [0.15000000000000002, 0.6666666666666666], [0.20000000000000004, 0.6666666666666666], [0.25000000000000006, 0.6666666666666666], [0.30000000000000004, 0.6666666666666666], [0.3500000000000001, 0.6666666666666666], [0.40000000000000013, 0.6666666666666666], [0.4500000000000001, 0.6666666666666666], [0.5000000000000001, 0.6666666666666666], [0.5500000000000002, 0.6666666666666666], [0.6000000000000002, 0.6666666666666666], [0.6500000000000001, 0.6666666666666666], [0.7000000000000002, 0.6666666666666666], [0.7500000000000002, 0.6666666666666666], [0.8000000000000003, 0.6666666666666666], [0.8500000000000003, 0.6666666666666666]]}}, "top_10_features": [{"feature": "averagecellv_max", "importance": 0.3989635705947876, "feature_type": "Original"}, {"feature": "lcellv_max", "importance": 0.3362729251384735, "feature_type": "Original"}, {"feature": "cellvdelta_min", "importance": 0.24452660977840424, "feature_type": "Original"}, {"feature": "systemvolt_max", "importance": 0.020236818119883537, "feature_type": "Original"}, {"feature": "cellvdelta_mean", "importance": 0.0, "feature_type": "Original"}, {"feature": "averagecellv__variance", "importance": 0.0, "feature_type": "TSFresh"}, {"feature": "totalcurrenta__mean", "importance": 0.0, "feature_type": "TSFresh"}, {"feature": "totalcurrenta__sum_values", "importance": 0.0, "feature_type": "TSFresh"}, {"feature": "totalcurrenta__variance", "importance": 0.0, "feature_type": "TSFresh"}, {"feature": "totalcurrenta__standard_deviation", "importance": 0.0, "feature_type": "TSFresh"}]}