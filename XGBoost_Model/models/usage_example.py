#!/usr/bin/env python3
"""
XGBoost异常检测模型使用示例
兼容XGBoost 3.0.2
"""

import pickle
import pandas as pd
import numpy as np
import json

def load_model():
    """加载训练好的模型和配置"""
    with open('models/xgboost_model.pkl', 'rb') as f:
        model = pickle.load(f)
    
    with open('models/threshold_config.json', 'r') as f:
        config = json.load(f)
    
    return model, config

def predict_anomaly(features_df, model, config):
    """
    预测电芯异常
    
    Args:
        features_df: 包含特征的DataFrame
        model: 训练好的XGBoost模型
        config: 阈值配置
    
    Returns:
        dict: 预测结果
    """
    # 确保特征顺序正确
    expected_features = config['feature_names']
    missing_features = [col for col in expected_features if col not in features_df.columns]
    
    if missing_features:
        raise ValueError(f"缺少特征列: {missing_features[:5]}...")
    
    # 选择并排序特征
    X = features_df[expected_features]
    
    # 预测
    probabilities = model.predict_proba(X)[:, 1]
    predictions = (probabilities >= config['best_threshold']).astype(int)
    
    return {
        'predictions': predictions,
        'probabilities': probabilities,
        'threshold': config['best_threshold'],
        'anomaly_count': predictions.sum(),
        'total_samples': len(predictions),
        'anomaly_rate': predictions.mean()
    }

# 使用示例
if __name__ == "__main__":
    print("🤖 XGBoost异常检测模型")
    
    # 加载模型
    model, config = load_model()
    print("✅ 模型加载成功")
    print(f"适配XGBoost版本: {config.get('xgboost_version', 'Unknown')}")
    print(f"最佳阈值: {config['best_threshold']:.3f}")
    print(f"特征数量: {len(config['feature_names'])}")
    
    # 示例预测（需要提供实际数据）
    # result = predict_anomaly(your_features_df, model, config)
    # print(f"检测结果: {result['anomaly_count']}/{result['total_samples']} 异常")
