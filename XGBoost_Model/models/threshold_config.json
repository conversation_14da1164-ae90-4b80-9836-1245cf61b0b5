{"best_threshold": 0.1, "threshold_optimization_method": "f1_score_maximization", "feature_names": ["cellvdelta__sum_values", "hcellv__absolute_maximum", "averagecellv__variance", "totalcurrenta__minimum", "averagecellv__minimum", "cellvdelta__absolute_maximum", "ltempc__standard_deviation", "soc__minimum", "lcellv__standard_deviation", "soc__standard_deviation", "cellvdelta__mean", "systemvolt__absolute_maximum", "totalcurrenta__root_mean_square", "systemvolt__root_mean_square", "htempc__root_mean_square", "cellvdelta__minimum", "ltempc__minimum", "averagecellv__standard_deviation", "lcellv__mean", "hcellv__root_mean_square", "lcellv__maximum", "ltempc__absolute_maximum", "lcellv__minimum", "cellvdelta__median", "lcellv__root_mean_square", "ltempc__sum_values", "lcellv__absolute_maximum", "totalcurrenta__sum_values", "htempc__median", "hcellv__median", "ltempc__maximum", "ltempc__variance", "cellvdelta__root_mean_square", "totalcurrenta__absolute_maximum", "systemvolt__median", "cellvdelta__variance", "averagecellv__sum_values", "totalcurrenta__standard_deviation", "soc__median", "systemvolt__standard_deviation", "cellvdelta__standard_deviation", "lcellv__median", "totalcurrenta__median", "ltempc__root_mean_square", "htempc__minimum", "systemvolt__sum_values", "htempc__mean", "averagecellv__absolute_maximum", "ltempc__median", "lcellv__variance", "averagecellv__maximum", "systemvolt__variance", "totalcurrenta__variance", "soc__variance", "cellvdelta__maximum", "totalcurrenta__mean", "systemvolt__minimum", "hcellv__mean", "lcellv__sum_values", "hcellv__maximum", "htempc__sum_values", "ltempc__mean", "averagecellv__median", "averagecellv__mean", "hcellv__minimum", "averagecellv__root_mean_square", "systemvolt__maximum", "systemvolt__mean", "hcellv__sum_values"], "xgboost_version": "3.0.2"}