电芯不平衡异常检测模型性能报告
=====================================

训练信息:
- 模型类型: XGBoost 3.0.2
- 训练时间: 2025-05-31 20:07:55
- 特征数量: 114

性能对比:
                基础模型    最终模型    提升
F1-Score        0.667      0.667      +0.000
AUC-ROC         0.833      0.833      +0.000
Precision       0.500      0.500      +0.000
Recall          1.000      1.000      +0.000

超参数调优:
- 测试组合数: 27
- 最佳F1分数: 0.667

最终配置:
- 分类阈值: 0.100
- AUC-ROC: 0.833
- AUC-PR: 0.500

混淆矩阵:
        预测
        正常  异常
实际 正常  20   10
     异常   0   10

使用说明:
1. 使用 models/usage_example.py 进行预测
2. 模型适用于电芯不平衡异常检测
3. 建议定期监控模型性能并重训练