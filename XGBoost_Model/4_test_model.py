#!/usr/bin/env python3
"""
在真实测试数据上评估模型性能
"""

import pandas as pd
import numpy as np
import pickle
import json
from datetime import datetime
from sklearn.metrics import classification_report, confusion_matrix, f1_score, precision_score, recall_score, roc_auc_score
import matplotlib.pyplot as plt
import seaborn as sns

def load_model_and_config():
    """加载训练好的模型和配置"""
    print("📁 加载模型和配置...")
    
    # 加载模型
    with open('models/xgboost_model.pkl', 'rb') as f:
        model = pickle.load(f)
    
    # 加载阈值配置
    with open('models/threshold_config.json', 'r') as f:
        threshold_config = json.load(f)
    
    print(f"   模型加载成功")
    print(f"   最佳阈值: {threshold_config['best_threshold']}")
    
    return model, threshold_config

def preprocess_test_data():
    """预处理测试数据"""
    print("\n🔄 预处理测试数据...")
    
    # 加载原始测试数据
    test_data = pd.read_csv('data/processed_data/test_raw_data.csv')
    print(f"   原始测试数据: {test_data.shape}")
    
    # 检查数据质量
    print(f"   时间范围: {test_data['ts'].min()} 到 {test_data['ts'].max()}")
    print(f"   数据点数: {len(test_data)}")
    
    # 检查是否有异常标签
    if 'is_string_imbalance' in test_data.columns:
        anomaly_count = test_data['is_string_imbalance'].sum()
        print(f"   异常数据点: {anomaly_count}/{len(test_data)} ({anomaly_count/len(test_data)*100:.1f}%)")
    else:
        print("   ⚠️ 测试数据中没有异常标签，将进行无监督预测")
    
    # 转换时间戳
    test_data['ts'] = pd.to_datetime(test_data['ts'])
    test_data = test_data.sort_values('ts').reset_index(drop=True)
    
    return test_data

def create_time_windows(test_data, window_size_seconds=30):
    """创建时间窗口（与训练时保持一致）"""
    print(f"\n🪟 创建时间窗口 (窗口大小: {window_size_seconds}秒)...")
    
    # 计算时间差
    test_data['time_diff'] = test_data['ts'].diff().dt.total_seconds()
    
    # 创建窗口ID
    window_id = 1
    window_ids = [window_id]
    current_window_start = test_data['ts'].iloc[0]
    
    for i in range(1, len(test_data)):
        time_since_window_start = (test_data['ts'].iloc[i] - current_window_start).total_seconds()
        
        if time_since_window_start >= window_size_seconds:
            window_id += 1
            current_window_start = test_data['ts'].iloc[i]
        
        window_ids.append(window_id)
    
    test_data['window_id'] = window_ids
    
    print(f"   创建了 {test_data['window_id'].nunique()} 个窗口")
    print(f"   每个窗口平均数据点数: {len(test_data) / test_data['window_id'].nunique():.1f}")
    
    return test_data

def extract_features(test_data):
    """提取特征（与训练时保持一致）"""
    print("\n🔧 提取特征...")
    
    # 定义核心特征（与训练脚本保持一致）
    CORE_FEATURES = [
        'cellvdelta', 'averagecellv', 'hcellv', 'lcellv', 
        'systemvolt', 'totalcurrenta', 'soc', 'htempc', 'ltempc'
    ]
    
    # 检查特征是否存在
    available_features = [f for f in CORE_FEATURES if f in test_data.columns]
    missing_features = [f for f in CORE_FEATURES if f not in test_data.columns]
    
    print(f"   可用特征: {len(available_features)}/{len(CORE_FEATURES)}")
    if missing_features:
        print(f"   缺失特征: {missing_features}")
    
    # 按窗口聚合特征
    print("   聚合原始特征...")
    test_agg_features = test_data.groupby('window_id')[available_features].agg([
        'mean', 'std', 'min', 'max', 'median'
    ]).round(6)
    
    # 展平多级列名
    test_agg_features.columns = ['_'.join(col).strip() for col in test_agg_features.columns.values]
    test_agg_features.reset_index(drop=True, inplace=True)
    
    print(f"   聚合特征形状: {test_agg_features.shape}")
    
    # 创建标签（如果存在异常标签）
    test_labels = None
    if 'is_string_imbalance' in test_data.columns:
        # 按窗口聚合标签（如果窗口中有任何异常，则标记为异常）
        test_labels = test_data.groupby('window_id')['is_string_imbalance'].max().reset_index()
        test_labels['label'] = test_labels['is_string_imbalance'].astype(int)
        print(f"   标签分布: {test_labels['label'].value_counts().to_dict()}")
    
    return test_agg_features, test_labels

def create_tsfresh_features(test_agg_features):
    """创建模拟的TSFresh特征（简化版本）"""
    print("\n⚙️ 创建TSFresh风格特征...")
    
    # 由于我们没有完整的TSFresh流程，创建一些基本的时序特征
    # 这是一个简化版本，实际应用中应该使用完整的TSFresh流程
    
    tsfresh_features = pd.DataFrame()
    
    # 为每个原始特征创建一些基本的TSFresh风格特征
    for col in test_agg_features.columns:
        if '_mean' in col:
            base_name = col.replace('_mean', '')
            tsfresh_features[f'{base_name}__maximum'] = test_agg_features[col.replace('_mean', '_max')]
            tsfresh_features[f'{base_name}__absolute_maximum'] = test_agg_features[col.replace('_mean', '_max')].abs()
            tsfresh_features[f'{base_name}__mean'] = test_agg_features[col]
            tsfresh_features[f'{base_name}__standard_deviation'] = test_agg_features[col.replace('_mean', '_std')]
            tsfresh_features[f'{base_name}__median'] = test_agg_features[col.replace('_mean', '_median')]
            tsfresh_features[f'{base_name}__minimum'] = test_agg_features[col.replace('_mean', '_min')]
            tsfresh_features[f'{base_name}__variance'] = test_agg_features[col.replace('_mean', '_std')] ** 2
    
    # 填充缺失值
    tsfresh_features = tsfresh_features.fillna(0)
    
    print(f"   TSFresh特征形状: {tsfresh_features.shape}")
    
    return tsfresh_features

def predict_on_test_data(model, test_features, threshold):
    """在测试数据上进行预测"""
    print(f"\n🔮 模型预测 (阈值: {threshold})...")
    
    # 获取预测概率
    y_pred_proba = model.predict_proba(test_features)[:, 1]
    
    # 应用阈值
    y_pred = (y_pred_proba >= threshold).astype(int)
    
    print(f"   预测概率范围: {y_pred_proba.min():.4f} - {y_pred_proba.max():.4f}")
    print(f"   预测结果分布:")
    print(f"     正常: {(y_pred == 0).sum()}")
    print(f"     异常: {(y_pred == 1).sum()}")
    print(f"     异常率: {y_pred.mean()*100:.1f}%")
    
    return y_pred_proba, y_pred

def evaluate_predictions(y_true, y_pred, y_pred_proba):
    """评估预测结果"""
    print("\n📊 评估预测结果...")
    
    # 计算性能指标
    f1 = f1_score(y_true, y_pred)
    precision = precision_score(y_true, y_pred, zero_division=0)
    recall = recall_score(y_true, y_pred, zero_division=0)
    auc = roc_auc_score(y_true, y_pred_proba)
    
    print(f"   F1-Score: {f1:.3f}")
    print(f"   Precision: {precision:.3f}")
    print(f"   Recall: {recall:.3f}")
    print(f"   AUC-ROC: {auc:.3f}")
    
    # 混淆矩阵
    cm = confusion_matrix(y_true, y_pred)
    print(f"\n   混淆矩阵:")
    print(f"   TN: {cm[0,0]:2d} | FP: {cm[0,1]:2d}")
    print(f"   FN: {cm[1,0]:2d} | TP: {cm[1,1]:2d}")
    
    # 详细分类报告
    print(f"\n   详细分类报告:")
    print(classification_report(y_true, y_pred, target_names=['正常', '异常']))
    
    return {
        'f1_score': f1,
        'precision': precision,
        'recall': recall,
        'auc_roc': auc,
        'confusion_matrix': cm.tolist()
    }

def save_test_results(results, y_pred_proba, y_pred, test_labels=None):
    """保存测试结果"""
    print("\n💾 保存测试结果...")
    
    # 保存性能指标
    results['test_date'] = datetime.now().isoformat()
    results['test_samples'] = len(y_pred)
    
    with open('results/test_performance.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    # 保存预测结果
    predictions_df = pd.DataFrame({
        'window_id': range(1, len(y_pred) + 1),
        'predicted_probability': y_pred_proba,
        'predicted_label': y_pred
    })
    
    if test_labels is not None:
        predictions_df['true_label'] = test_labels['label'].values
    
    predictions_df.to_csv('results/test_predictions.csv', index=False)
    
    print(f"   测试结果已保存:")
    print(f"     性能指标: results/test_performance.json")
    print(f"     预测结果: results/test_predictions.csv")

def main():
    print("🧪 开始测试模型性能...")
    print("=" * 60)
    
    # 1. 加载模型和配置
    model, threshold_config = load_model_and_config()
    
    # 2. 预处理测试数据
    test_data = preprocess_test_data()
    
    # 3. 创建时间窗口
    test_data = create_time_windows(test_data)
    
    # 4. 提取特征
    test_agg_features, test_labels = extract_features(test_data)
    
    # 5. 创建TSFresh特征
    test_tsfresh_features = create_tsfresh_features(test_agg_features)
    
    # 6. 合并特征
    test_features = pd.concat([test_agg_features, test_tsfresh_features], axis=1)
    print(f"\n🔗 合并后特征形状: {test_features.shape}")
    
    # 7. 进行预测
    y_pred_proba, y_pred = predict_on_test_data(
        model, test_features, threshold_config['best_threshold']
    )
    
    # 8. 评估结果（如果有标签）
    if test_labels is not None:
        results = evaluate_predictions(test_labels['label'].values, y_pred, y_pred_proba)
        
        # 9. 保存结果
        save_test_results(results, y_pred_proba, y_pred, test_labels)
        
        print(f"\n🎯 测试完成！模型在真实测试数据上的表现:")
        print(f"   F1-Score: {results['f1_score']:.3f}")
        print(f"   Precision: {results['precision']:.3f}")
        print(f"   Recall: {results['recall']:.3f}")
        print(f"   AUC-ROC: {results['auc_roc']:.3f}")
        
    else:
        print(f"\n🔮 预测完成！由于没有真实标签，无法计算性能指标")
        print(f"   预测了 {len(y_pred)} 个窗口")
        print(f"   异常窗口数: {y_pred.sum()}")
        
        # 保存预测结果
        save_test_results({}, y_pred_proba, y_pred)
    
    print("\n✅ 测试流程完成！")

if __name__ == "__main__":
    main()
