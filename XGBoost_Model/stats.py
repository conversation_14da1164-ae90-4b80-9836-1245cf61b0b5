import pandas as pd
import warnings
warnings.filterwarnings('ignore')

def clean_data_by_minute(df):
    """
    数据清理：按分钟采样，每分钟只保留一条数据
    """
    print("🔧 开始数据清理...")
    
    # 转换时间戳
    if df['ts'].dtype == 'float64':
        # Excel日期格式转换
        df['ts'] = pd.to_datetime(df['ts'], unit='D', origin='1900-01-01')
    else:
        df['ts'] = pd.to_datetime(df['ts'])
    
    print(f"原始数据: {len(df)} 行")
    
    # 按分钟分组，取每分钟的第一条记录
    df['minute'] = df['ts'].dt.floor('min')
    cleaned_df = df.groupby('minute').first().reset_index()
    
    print(f"清理后数据: {len(cleaned_df)} 行")
    print(f"数据压缩率: {(1 - len(cleaned_df)/len(df))*100:.1f}%\n")
    
    return cleaned_df

def detect_anomalies(df):
    """
    储能系统异常检测
    """
    print("🔍 开始异常检测...")
    
    anomalies = {
        'SYSTEM_STATUS_ERROR': 0,
        'ZERO_VOLTAGE': 0, 
        'String_Low_Volt': 0,
        'String_High_Volt': 0,
        'HIGH_CELL_VOLTAGE': 0,
        'LOW_CELL_VOLTAGE': 0,
        'EXTREME_HIGH_CELL_TEMP': 0,
        'PCS_FAULT': 0,
        'STRING_IMBALANCE': 0,
        'CSC_FAILURE': 0
    }
    
    anomaly_details = []
    
    for idx, row in df.iterrows():
        row_num = idx + 1
        
        # 1. 系统状态错误
        if row['sysstatus'] == 5:
            anomalies['SYSTEM_STATUS_ERROR'] += 1
            anomaly_details.append(f"第{row_num}行 [{row['ts']}]: 系统状态错误 (sysstatus = {row['sysstatus']})")
        
        # 2. 零电压
        if row['systemvolt'] == 0:
            anomalies['ZERO_VOLTAGE'] += 1
            anomaly_details.append(f"第{row_num}行 [{row['ts']}]: 零电压 (systemvolt = {row['systemvolt']})")
        
        # 3. 电芯低压
        if row['averagecellv'] < 2.8 and row['averagecellv'] != 0:
            anomalies['String_Low_Volt'] += 1
            anomaly_details.append(f"第{row_num}行 [{row['ts']}]: 电芯低压 (averagecellv = {row['averagecellv']:.3f}V)")
        
        # 4. 电芯高压  
        if row['averagecellv'] > 3.5:
            anomalies['String_High_Volt'] += 1
            anomaly_details.append(f"第{row_num}行 [{row['ts']}]: 电芯高压 (averagecellv = {row['averagecellv']:.3f}V)")
        
        # 5. 最高电芯电压过高
        if row['hcellv'] > 3.6:
            anomalies['HIGH_CELL_VOLTAGE'] += 1
            anomaly_details.append(f"第{row_num}行 [{row['ts']}]: 最高电芯电压过高 (hcellv = {row['hcellv']:.3f}V)")
        
        # 6. 最低电芯电压过低
        if row['lcellv'] < 2.7:
            anomalies['LOW_CELL_VOLTAGE'] += 1
            anomaly_details.append(f"第{row_num}行 [{row['ts']}]: 最低电芯电压过低 (lcellv = {row['lcellv']:.3f}V)")
        
        # 7. 电芯极高温
        if row['htempc'] >= 50:
            anomalies['EXTREME_HIGH_CELL_TEMP'] += 1
            anomaly_details.append(f"第{row_num}行 [{row['ts']}]: 电芯极高温 (htempc = {row['htempc']}°C)")
        
        # 8. PCS故障
        if row['pcspower'] == 0 and row['pcspowerset'] != 0:
            anomalies['PCS_FAULT'] += 1
            anomaly_details.append(f"第{row_num}行 [{row['ts']}]: PCS故障 (pcspower = {row['pcspower']}, pcspowerset = {row['pcspowerset']})")
        
        # 9. 电芯不平衡
        if row['cellvdelta'] >= 0.3 and row['averagecellv'] >= 3.1:
            anomalies['STRING_IMBALANCE'] += 1
            anomaly_details.append(f"第{row_num}行 [{row['ts']}]: 电芯不平衡 (cellvdelta = {row['cellvdelta']:.3f}V, averagecellv = {row['averagecellv']:.3f}V)")
        
        # 10. CSC故障
        if row['sysstatus'] == 5 and row['lcellv'] == 0 and row['averagecellv'] != 0:
            anomalies['CSC_FAILURE'] += 1
            anomaly_details.append(f"第{row_num}行 [{row['ts']}]: CSC故障 (sysstatus = {row['sysstatus']}, lcellv = {row['lcellv']}, averagecellv = {row['averagecellv']:.3f})")
    
    return anomalies, anomaly_details

def generate_report(df, anomalies, anomaly_details):
    """
    生成异常检测报告
    """
    print("="*60)
    print("🔋 储能系统异常检测报告")
    print("="*60)
    
    # 基本信息
    print(f"\n📊 数据概况:")
    print(f"  MAC ID: {df['macid'].iloc[0]}")
    print(f"  数据行数: {len(df)}")
    print(f"  时间范围: {df['ts'].min()} 至 {df['ts'].max()}")
    
    # 异常检测结果
    print(f"\n🚨 异常检测结果:")
    if anomaly_details:
        for detail in anomaly_details:
            print(f"  ⚠️  {detail}")
    else:
        print("  ✅ 未检测到任何异常！")
    
    # 异常统计
    print(f"\n📈 异常统计汇总:")
    total_anomalies = sum(anomalies.values())
    for anomaly_type, count in anomalies.items():
        icon = "🔴" if count > 0 else "✅"
        print(f"  {icon} {anomaly_type}: {count} 条")
    
    # 异常率
    anomaly_rate = (total_anomalies / len(df)) * 100
    print(f"\n📊 整体异常率: {total_anomalies}/{len(df)} = {anomaly_rate:.2f}%")
    
    # 关键参数统计
    print(f"\n📐 关键参数范围:")
    print(f"  平均电芯电压: {df['averagecellv'].min():.3f}V - {df['averagecellv'].max():.3f}V")
    print(f"  电芯电压差: {df['cellvdelta'].min():.3f}V - {df['cellvdelta'].max():.3f}V") 
    print(f"  系统电压: {df['systemvolt'].min():.1f}V - {df['systemvolt'].max():.1f}V")
    print(f"  最高温度: {df['htempc'].min()}°C - {df['htempc'].max()}°C")
    print(f"  SOC范围: {df['soc'].min():.1f}% - {df['soc'].max():.1f}%")

def main():
    """
    主函数
    """
    try:
        # 读取数据 (支持CSV和Excel)
        file_path = "continuous_2024H1_MAC_8C_1F_64_C5_12_52.csv"  # 修改为你的文件路径
        
        if file_path.endswith('.csv'):
            df = pd.read_csv(file_path)
        else:
            df = pd.read_excel(file_path)
        
        # 数据清理
        cleaned_df = clean_data_by_minute(df)
        
        # 异常检测
        anomalies, anomaly_details = detect_anomalies(cleaned_df)
        
        # 生成报告
        generate_report(cleaned_df, anomalies, anomaly_details)
        
        # 保存清理后的数据
        output_file = f"cleaned_{file_path}"
        cleaned_df.to_csv(output_file, index=False)
        print(f"\n💾 清理后数据已保存至: {output_file}")
        
    except Exception as e:
        print(f"❌ 处理过程中出现错误: {e}")

if __name__ == "__main__":
    main()
