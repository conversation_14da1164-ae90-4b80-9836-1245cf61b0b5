#!/usr/bin/env python3

# 手动创建随机划分的标签文件
import random

# 设置随机种子
random.seed(42)

# 创建所有窗口ID和标签的列表
# 根据之前的分析：窗口1-40是异常(1)，窗口41-200是正常(0)
all_data = []

# 异常窗口 (1-40)
for i in range(1, 41):
    all_data.append((i, 1))

# 正常窗口 (41-200) 
for i in range(41, 201):
    all_data.append((i, 0))

print(f"总窗口数: {len(all_data)}")
print(f"异常窗口: {sum(1 for _, label in all_data if label == 1)}")
print(f"正常窗口: {sum(1 for _, label in all_data if label == 0)}")

# 随机打乱
random.shuffle(all_data)

# 按80/20划分
train_size = int(len(all_data) * 0.8)
train_data = all_data[:train_size]
val_data = all_data[train_size:]

print(f"训练集: {len(train_data)} 窗口")
print(f"验证集: {len(val_data)} 窗口")

# 统计新的分布
train_anomalies = sum(1 for _, label in train_data if label == 1)
val_anomalies = sum(1 for _, label in val_data if label == 1)

print(f"训练集异常: {train_anomalies}/{len(train_data)} ({train_anomalies/len(train_data)*100:.1f}%)")
print(f"验证集异常: {val_anomalies}/{len(val_data)} ({val_anomalies/len(val_data)*100:.1f}%)")

# 写入新的标签文件
with open('data/processed_data/train_feature_labels_fixed.csv', 'w') as f:
    f.write('window_id,label\n')
    for window_id, label in train_data:
        f.write(f'{window_id},{label}\n')

with open('data/processed_data/val_feature_labels_fixed.csv', 'w') as f:
    f.write('window_id,label\n')
    for window_id, label in val_data:
        f.write(f'{window_id},{label}\n')

print("✅ 修复完成! 新文件已保存为 *_fixed.csv")
