import pandas as pd
import numpy as np
import os
import matplotlib.pyplot as plt
import seaborn as sns

# 创建输出目录
os.makedirs('data/model_ready', exist_ok=True)
os.makedirs('data/model_ready/plots', exist_ok=True)  # 为可视化创建目录

# 读取合并后的数据
combined_df = pd.read_csv('data/combined_data/combined_dataset.csv')

# 确保时间列是datetime类型
combined_df['ts'] = pd.to_datetime(combined_df['ts'])

# 1. 定义所有异常标签 - 从数据中动态获取
print("检查数据中的异常标签...")
print("Checking anomaly labels in data...")
potential_label_columns = [col for col in combined_df.columns if col not in ['Label', 'id', 'ts', 'NORMAL'] and col[0].isupper()]
anomaly_labels = []

for col in potential_label_columns:
    if combined_df[col].sum() > 0:  # 只包含实际存在的异常类型
        anomaly_labels.append(col)
        print(f"发现异常标签: {col}, 数量: {combined_df[col].sum()}")
        print(f"Found anomaly label: {col}, count: {combined_df[col].sum()}")

# 如果没有找到任何标签，使用预定义的列表
if not anomaly_labels:
    anomaly_labels = [
        'HIGH_CELL_VOLTAGE', 
        'LOW_CELL_VOLTAGE', 
        'EXTREME_HIGH_CELL_TEMP',
        'String_High_Volt',
        'String_Low_Volt',
        'CSC_FAILURE',
        'ZERO_VOLTAGE',
        'SYSTEM_STATUS_ERROR',
        'STRING_IMBALANCE',
        'PCS_FAULT'
    ]
    print("使用预定义的异常标签列表")
    print("Using predefined anomaly label list")

# 检查是否所有标签都在数据中
existing_labels = []
for label in anomaly_labels:
    if label in combined_df.columns:
        existing_labels.append(label)
    else:
        print(f"警告: 标签 {label} 在数据中不存在")
        print(f"Warning: Label {label} does not exist in data")

print(f"找到 {len(existing_labels)} 个有效的异常标签")
print(f"Found {len(existing_labels)} valid anomaly labels")

# 2. 添加NORMAL标签列
# 当所有异常标签都为0时，NORMAL为1
if 'NORMAL' not in combined_df.columns:
    print("添加NORMAL标签...")
    print("Adding NORMAL label...")
    combined_df['NORMAL'] = 0
    normal_mask = (combined_df[existing_labels].sum(axis=1) == 0)
    combined_df.loc[normal_mask, 'NORMAL'] = 1
    print(f"标记了 {normal_mask.sum()} 条正常记录")
    print(f"Marked {normal_mask.sum()} normal records")

# 3. 准备特征和标签
print("准备特征和标签...")
print("Preparing features and labels...")
# 排除不需要的列
exclude_columns = ['Label'] + existing_labels + ['NORMAL']
feature_columns = [col for col in combined_df.columns if col not in exclude_columns]

# 创建特征矩阵
X = combined_df[feature_columns].copy()

# 创建标签矩阵 (包括所有异常标签和NORMAL标签)
all_labels = existing_labels + ['NORMAL']
y = combined_df[all_labels].copy()

# 4. 处理缺失值
print("处理缺失值...")
print("Handling missing values...")
# 对于数值型特征，用中位数填充
numeric_features = X.select_dtypes(include=['int64', 'float64']).columns
X[numeric_features] = X[numeric_features].fillna(X[numeric_features].median())

# 对于分类特征，用众数填充
categorical_features = X.select_dtypes(include=['object']).columns
for col in categorical_features:
    X[col] = X[col].fillna(X[col].mode()[0] if not X[col].mode().empty else 'unknown')

# 5. 检查特征分布
print("分析特征分布...")
print("Analyzing feature distributions...")
# 检查数值特征的分布，识别可能的异常值
for col in numeric_features[:5]:  # 只检查前5个特征作为示例
    Q1 = X[col].quantile(0.25)
    Q3 = X[col].quantile(0.75)
    IQR = Q3 - Q1
    outlier_count = ((X[col] < (Q1 - 1.5 * IQR)) | (X[col] > (Q3 + 1.5 * IQR))).sum()
    if outlier_count > 0:
        print(f"特征 {col} 有 {outlier_count} 个异常值 ({outlier_count/len(X)*100:.2f}%)")
        print(f"Feature {col} has {outlier_count} outliers ({outlier_count/len(X)*100:.2f}%)")

# 6. 保存处理后的数据
print("保存处理后的数据...")
print("Saving processed data...")
X.to_csv('data/model_ready/features.csv', index=False)
y.to_csv('data/model_ready/labels.csv', index=False)

# 7. 可视化标签分布
print("生成标签分布可视化...")
print("Generating label distribution visualizations...")
plt.figure(figsize=(12, 6))
label_counts = y.sum().sort_values(ascending=False)
sns.barplot(x=label_counts.index, y=label_counts.values)
plt.title('Anomaly Type Distribution')
plt.xlabel('Anomaly Types')
plt.ylabel('Count')
plt.xticks(rotation=45, ha='right')
plt.tight_layout()
plt.savefig('data/model_ready/plots/label_distribution.png')

# 添加更多有用的可视化
# 异常与正常样本比例饼图
plt.figure(figsize=(10, 6))
normal_count = y['NORMAL'].sum()
anomaly_count = len(y) - normal_count
plt.pie([normal_count, anomaly_count], 
        labels=['Normal', 'Anomaly'], 
        autopct='%1.1f%%',
        colors=['#66b3ff', '#ff9999'],
        explode=(0, 0.1))
plt.title('Normal vs Anomaly Samples')
plt.tight_layout()
plt.savefig('data/model_ready/plots/normal_anomaly_ratio.png')

# 如果有时间特征，添加时间序列分布图
if 'ts' in combined_df.columns:
    plt.figure(figsize=(14, 6))
    # 按日期统计异常数量
    combined_df['date'] = combined_df['ts'].dt.date
    anomaly_by_date = combined_df.groupby('date')[existing_labels].sum().sum(axis=1)
    anomaly_by_date.plot(kind='line', color='red')
    plt.title('Anomaly Distribution Over Time')
    plt.xlabel('Date')
    plt.ylabel('Number of Anomalies')
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.tight_layout()
    plt.savefig('data/model_ready/plots/anomaly_time_distribution.png')

# 8. 打印数据统计信息
print(f"\n特征数量: {X.shape[1]}")
print(f"Number of features: {X.shape[1]}")
print(f"样本数量: {X.shape[0]}")
print(f"Number of samples: {X.shape[0]}")
print("\n标签分布:")
print("Label distribution:")
for label in all_labels:
    count = y[label].sum()
    percentage = (count / len(y)) * 100
    print(f"{label}: {count} ({percentage:.2f}%)")
    print(f"{label}: {count} ({percentage:.2f}%)")

print(f"\n多标签样本数量: {(y.sum(axis=1) > 1).sum()}")
print(f"Number of multi-label samples: {(y.sum(axis=1) > 1).sum()}")
print(f"正常样本数量: {y['NORMAL'].sum()}")
print(f"Number of normal samples: {y['NORMAL'].sum()}")

# 9. 检查类别不平衡情况
imbalance_ratio = y['NORMAL'].sum() / (len(y) - y['NORMAL'].sum()) if (len(y) - y['NORMAL'].sum()) > 0 else float('inf')
print(f"正常/异常样本比例: {imbalance_ratio:.2f}")
print(f"Normal/Anomaly ratio: {imbalance_ratio:.2f}")
if imbalance_ratio > 10:
    print("警告: 数据严重不平衡，可能需要采用平衡技术")
    print("Warning: Data is severely imbalanced, balancing techniques may be needed")
