import os
import numpy as np
import pandas as pd
from tsfresh import extract_features
from tsfresh.utilities.dataframe_functions import impute
from tsfresh.feature_extraction import MinimalFCParameters

# ────────────────────────────────────────────────
# 0. Paths & Settings
# ────────────────────────────────────────────────
DATA_DIR = "data/model_ready"
OUT_DIR  = "data/processed"
os.makedirs(OUT_DIR, exist_ok=True)

WINDOW_MINUTES = 30
STEP_MINUTES   = 30
CORE_COLS = ["Volt", "PcsPower", "CellV", "CellTempC"]
DROP_COLS = ["localip", "softversion", "balancestatus", "mins"]

# ────────────────────────────────────────────────
# 1. Load and clean data
# ────────────────────────────────────────────────
features_df = pd.read_csv(f"{DATA_DIR}/features.csv", parse_dates=["ts"])
labels_df   = pd.read_csv(f"{DATA_DIR}/labels.csv")

features_df.drop(columns=[c for c in DROP_COLS if c in features_df.columns], inplace=True)
features_df = features_df[["macid", "ts"] + CORE_COLS].copy()

# align rows
labels_df = labels_df.loc[features_df.index].reset_index(drop=True)
features_df = features_df.reset_index(drop=True)
LABEL_COLS = [c for c in labels_df.columns if c != "id"]

# ────────────────────────────────────────────────
# 2. Segment by device and extract windows
# ────────────────────────────────────────────────
features_df.sort_values(["macid", "ts"], inplace=True)

segments = []
segment_id = 0
for macid, df_dev in features_df.groupby("macid"):
    df_dev = df_dev.reset_index(drop=False)
    start = 0
    while start + WINDOW_MINUTES <= len(df_dev):
        idx_slice = df_dev.index[start : start + WINDOW_MINUTES]
        segment_rows = df_dev.loc[idx_slice].copy()
        segment_rows["segment_id"] = segment_id
        segments.append(segment_rows)
        segment_id += 1
        start += STEP_MINUTES

seg_df = pd.concat(segments, ignore_index=True)

# Merge corresponding labels for each window (max rule)
label_windows = (
    seg_df[["segment_id"]]
    .join(labels_df.loc[seg_df["index"]].reset_index(drop=True))
    .groupby("segment_id").max()
)

# ────────────────────────────────────────────────
# 3. TSFRESH extraction
# ────────────────────────────────────────────────
print("▶ Extracting tsfresh features …")
X_tsfresh = extract_features(
    seg_df,
    column_id="segment_id",
    column_sort="ts",
    default_fc_parameters=MinimalFCParameters(),
)
impute(X_tsfresh)

# ────────────────────────────────────────────────
# 4. Save output for modeling
# ────────────────────────────────────────────────
X_tsfresh.to_csv(f"{OUT_DIR}/tsfresh_features.csv")
label_windows.to_csv(f"{OUT_DIR}/tsfresh_labels.csv")

print(f"Done. Features saved to: {OUT_DIR}/tsfresh_features.csv")
print(f"Labels saved to:   {OUT_DIR}/tsfresh_labels.csv")
