import pandas as pd
import os
import glob

# 指定包含所有CSV文件的文件夹路径
folder_path = 'data/anomaly_datasets'

# 获取所有CSV文件路径
csv_files = glob.glob(os.path.join(folder_path, '*.csv'))

# 创建空列表存储各个DataFrame
dfs = []

# 读取每个CSV文件
for file in csv_files:
    try:
        # 尝试使用utf-8-sig编码读取（处理带BOM的UTF-8文件）
        df = pd.read_csv(file, encoding='utf-8-sig')
    except UnicodeDecodeError:
        try:
            # 如果失败，尝试使用latin-1编码（更宽容的编码）
            df = pd.read_csv(file, encoding='latin-1')
            print(f"使用latin-1编码读取文件: {file}")
        except Exception as e:
            print(f"无法读取文件 {file}: {e}")
            continue
    
    # 清理标签中可能的不可见字符
    if 'Label' in df.columns:
        # 使用正则表达式移除所有不可打印字符和零宽字符
        df['Label'] = df['Label'].str.strip().str.replace(r'[\u200B-\u200D\uFEFF\u0000-\u001F\u007F-\u009F]', '', regex=True)
        # 修复 String_8igh_Volt 问题
        df['Label'] = df['Label'].str.replace('String_8igh_Volt', 'String_High_Volt')
        # 统一PCS FAULT标签格式（将空格替换为下划线）
        df['Label'] = df['Label'].str.replace('PCS FAULT', 'PCS_FAULT')
    
    # 确保时间戳是datetime类型
    if 'ts' in df.columns:
        df['ts'] = pd.to_datetime(df['ts'])
        
        # 检查文件内部是否有重复的时间戳和设备ID
        if 'stringid' in df.columns:
            file_name = os.path.basename(file)
            duplicates = df.duplicated(subset=['ts', 'stringid'], keep=False)
            dup_count = duplicates.sum()
            
            if dup_count > 0:
                print(f"{file_name}内部有{dup_count}行重复数据")
                
                # 为重复记录添加微小时间偏移
                # 按时间戳和设备ID分组
                for (ts, device_id), group in df.groupby(['ts', 'stringid']):
                    if len(group) > 1:
                        # 为每条记录添加递增的秒数
                        for i, idx in enumerate(group.index):
                            if i > 0:  # 第一条保持不变
                                df.at[idx, 'ts'] = ts + pd.Timedelta(seconds=i)
                
                print(f"已为{file_name}中的重复记录添加时间偏移")
    
    # 添加到列表
    dfs.append(df)

# 合并所有DataFrame
combined_df = pd.concat(dfs, ignore_index=True)

# 按时间排序
combined_df['ts'] = pd.to_datetime(combined_df['ts'])  # 确保ts是datetime类型
combined_df = combined_df.sort_values('ts')

# 修正：检查时间戳和设备ID组合是否有重复
# 这些是确定唯一数据行的关键字段
unique_identifiers = ['ts', 'stringid']
duplicates = combined_df.duplicated(subset=unique_identifiers, keep=False)

if duplicates.any():
    print(f"发现{duplicates.sum()}行具有相同时间戳和设备ID的数据")
    
    # 创建临时DataFrame来存储处理后的结果
    result_df = pd.DataFrame()
    
    # 按唯一标识符分组处理
    for group_key, group in combined_df.groupby(unique_identifiers):
        if len(group) > 1:
            # 这组有多个标签
            all_labels = group['Label'].unique()
            print(f"时间 {group_key[0]} 设备ID {group_key[1]} 具有多个标签: {all_labels}")
            
            # 合并标签 - 取第一行的数据，但更新标签列
            merged_row = group.iloc[0].copy()
            merged_row['Label'] = "+".join(all_labels)
            
            # 添加到结果
            result_df = pd.concat([result_df, pd.DataFrame([merged_row])], ignore_index=True)
        else:
            # 没有重复，直接添加
            result_df = pd.concat([result_df, group], ignore_index=True)
    
    # 替换原始DataFrame
    combined_df = result_df

# 获取所有唯一标签
all_labels = []
for label in combined_df['Label']:
    # 处理复合标签，例如"Label1+Label2"
    if "+" in label:
        parts = label.split("+")
        all_labels.extend(parts)
    else:
        all_labels.append(label)
        
unique_labels = list(set(all_labels))
print(f"所有唯一标签: {unique_labels}")

# 创建多标签列
for label in unique_labels:
    # 检查Label列是否包含此标签（考虑复合标签情况）
    combined_df[label] = combined_df['Label'].apply(lambda x: 1 if label in x.split("+") else 0)

# 保存合并后的数据
combined_df.to_csv('data/combined_data/combined_dataset.csv', index=False, encoding='utf-8')

# 查看标签分布
label_counts = {label: combined_df[label].sum() for label in unique_labels}
print("各类型标签数量:")
for label, count in label_counts.items():
    print(f"{label}: {count}")

# 检查重叠标签情况
overlap_count = (combined_df[unique_labels].sum(axis=1) > 1).sum()
print(f"存在多重标签的行数: {overlap_count}")
