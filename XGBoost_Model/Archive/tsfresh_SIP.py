"""
TSfresh + XGBoost 电芯不平衡异常检测流水线
基于窗口时序数据进行特征提取和模型训练

Usage:
    python tsfresh_xgboost_pipeline.py
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score, roc_curve
from sklearn.preprocessing import StandardScaler
import xgboost as xgb
import joblib
import os
from datetime import datetime

# TSfresh相关导入
from tsfresh import extract_features
from tsfresh.utilities.dataframe_functions import impute
from tsfresh.feature_extraction import MinimalFCParameters, EfficientFCParameters
from tsfresh.feature_selection import select_features

import warnings
warnings.filterwarnings('ignore')

class TSfreshXGBoostPipeline:
    def __init__(self, window_csv_path, output_dir="models"):
        """
        初始化TSfresh + XGBoost流水线
        
        Args:
            window_csv_path: 窗口数据CSV文件路径
            output_dir: 模型和结果输出目录
        """
        self.window_csv_path = window_csv_path
        self.output_dir = output_dir
        self.model = None
        self.scaler = None
        self.selected_features = None
        
        os.makedirs(output_dir, exist_ok=True)
    
    def load_and_prepare_data(self):
        """加载并准备数据"""
        print("📁 加载窗口数据...")
        
        df = pd.read_csv(self.window_csv_path)
        print(f"原始数据: {len(df)} 行")
        print(f"包含窗口: {df['window_id'].nunique()} 个")
        print(f"异常窗口: {(df.groupby('window_id')['label'].first() == 1).sum()} 个")
        print(f"正常窗口: {(df.groupby('window_id')['label'].first() == 0).sum()} 个")
        
        # 检查数据格式
        print(f"\n📊 数据格式检查:")
        print(f"时间列: {'ts' in df.columns}")
        print(f"窗口ID列: {'window_id' in df.columns}")
        print(f"标签列: {'label' in df.columns}")
        print(f"异常标记列: {'is_string_imbalance' in df.columns}")
        
        # 转换时间戳
        df['ts'] = pd.to_datetime(df['ts'])
        
        # 检查每个窗口的数据点数
        points_per_window = df.groupby('window_id').size()
        print(f"每个窗口平均数据点: {points_per_window.mean():.1f}")
        print(f"数据点数范围: {points_per_window.min()} - {points_per_window.max()}")
        
        return df
    
    def extract_tsfresh_features(self, df, feature_set="minimal"):
        """使用TSfresh提取时序特征"""
        print(f"\n🔧 提取TSfresh特征 (使用{feature_set}参数集)...")
        
        # 选择数值列进行特征提取
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        exclude_cols = ['window_id', 'label', 'is_string_imbalance']
        feature_cols = [col for col in numeric_cols if col not in exclude_cols]
        
        print(f"用于特征提取的列: {len(feature_cols)} 个")
        print(f"特征列示例: {feature_cols[:5]}")
        
        # 准备TSfresh格式的数据
        tsfresh_df = df[['window_id', 'ts'] + feature_cols].copy()
        
        # 选择特征计算参数
        if feature_set == "minimal":
            fc_parameters = MinimalFCParameters()
        elif feature_set == "efficient":
            fc_parameters = EfficientFCParameters()
        else:
            fc_parameters = None  # 使用默认全套特征
        
        # 提取特征
        print("正在提取特征...")
        start_time = datetime.now()
        
        X = extract_features(
            tsfresh_df,
            column_id="window_id",
            column_sort="ts",
            default_fc_parameters=fc_parameters,
            disable_progressbar=False
        )
        
        end_time = datetime.now()
        print(f"特征提取完成，耗时: {end_time - start_time}")
        print(f"提取的特征数: {X.shape[1]}")
        
        # 处理缺失值和无穷值
        print("处理缺失值和异常值...")
        X = impute(X)
        
        # 获取标签
        y = df.groupby("window_id")["label"].first()
        
        # 确保索引对齐
        X = X.loc[y.index]
        
        print(f"最终特征矩阵: {X.shape}")
        print(f"标签分布: {y.value_counts().to_dict()}")
        
        return X, y
    
    def feature_selection(self, X, y, selection_method="statistical"):
        """特征选择"""
        print(f"\n🎯 进行特征选择 (方法: {selection_method})...")
        
        if selection_method == "statistical" and len(X.columns) > 50:
            # 使用TSfresh的统计学特征选择
            print("使用TSfresh统计学特征选择...")
            X_selected = select_features(X, y)
            selected_features = X_selected.columns.tolist()
        else:
            # 跳过特征选择或特征数较少
            X_selected = X
            selected_features = X.columns.tolist()
        
        print(f"选择后的特征数: {len(selected_features)}")
        
        # 保存特征名称
        self.selected_features = selected_features
        
        return X_selected, selected_features
    
    def train_model(self, X, y, test_size=0.2, random_state=42):
        """训练XGBoost模型"""
        print(f"\n🚀 训练XGBoost模型...")
        
        # 分割训练和验证集
        X_train, X_val, y_train, y_val = train_test_split(
            X, y, test_size=test_size, random_state=random_state, 
            stratify=y
        )
        
        print(f"训练集: {len(X_train)} 个窗口")
        print(f"验证集: {len(X_val)} 个窗口")
        print(f"训练集异常比例: {y_train.mean():.2%}")
        print(f"验证集异常比例: {y_val.mean():.2%}")
        
        # 特征标准化
        self.scaler = StandardScaler()
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_val_scaled = self.scaler.transform(X_val)
        
        # 计算类别权重
        pos_weight = (len(y_train) - y_train.sum()) / y_train.sum()
        print(f"正负样本权重比: {pos_weight:.2f}")
        
        # 训练XGBoost
        self.model = xgb.XGBClassifier(
            scale_pos_weight=pos_weight,
            max_depth=6,
            n_estimators=100,
            learning_rate=0.1,
            random_state=random_state,
            eval_metric='auc'
        )
        
        # 训练模型
        self.model.fit(
            X_train_scaled, y_train,
            eval_set=[(X_val_scaled, y_val)],
            early_stopping_rounds=10,
            verbose=False
        )
        
        return X_train_scaled, X_val_scaled, y_train, y_val
    
    def evaluate_model(self, X_train, X_val, y_train, y_val):
        """评估模型性能"""
        print(f"\n📊 模型性能评估...")
        
        # 预测
        y_train_pred = self.model.predict(X_train)
        y_val_pred = self.model.predict(X_val)
        y_train_proba = self.model.predict_proba(X_train)[:, 1]
        y_val_proba = self.model.predict_proba(X_val)[:, 1]
        
        # 计算指标
        train_auc = roc_auc_score(y_train, y_train_proba)
        val_auc = roc_auc_score(y_val, y_val_proba)
        
        print(f"训练集 AUC: {train_auc:.4f}")
        print(f"验证集 AUC: {val_auc:.4f}")
        
        print(f"\n训练集分类报告:")
        print(classification_report(y_train, y_train_pred))
        
        print(f"\n验证集分类报告:")
        print(classification_report(y_val, y_val_pred))
        
        # 混淆矩阵
        train_cm = confusion_matrix(y_train, y_train_pred)
        val_cm = confusion_matrix(y_val, y_val_pred)
        
        # 特征重要性
        feature_importance = pd.DataFrame({
            'feature': self.selected_features,
            'importance': self.model.feature_importances_
        }).sort_values('importance', ascending=False)
        
        print(f"\n🔝 Top 10 重要特征:")
        print(feature_importance.head(10))
        
        return {
            'train_auc': train_auc,
            'val_auc': val_auc,
            'train_cm': train_cm,
            'val_cm': val_cm,
            'feature_importance': feature_importance,
            'y_val_proba': y_val_proba,
            'y_val_true': y_val
        }
    
    def save_model_and_results(self, results):
        """保存模型和结果"""
        print(f"\n💾 保存模型和结果到 {self.output_dir}...")
        
        # 保存模型
        model_path = os.path.join(self.output_dir, 'xgboost_model.pkl')
        joblib.dump(self.model, model_path)
        
        # 保存预处理器
        scaler_path = os.path.join(self.output_dir, 'scaler.pkl')
        joblib.dump(self.scaler, scaler_path)
        
        # 保存特征名称
        features_path = os.path.join(self.output_dir, 'selected_features.txt')
        with open(features_path, 'w') as f:
            for feature in self.selected_features:
                f.write(f"{feature}\n")
        
        # 保存特征重要性
        importance_path = os.path.join(self.output_dir, 'feature_importance.csv')
        results['feature_importance'].to_csv(importance_path, index=False)
        
        # 保存模型性能
        metrics_path = os.path.join(self.output_dir, 'model_metrics.txt')
        with open(metrics_path, 'w') as f:
            f.write(f"Training AUC: {results['train_auc']:.4f}\n")
            f.write(f"Validation AUC: {results['val_auc']:.4f}\n")
            f.write(f"Feature Count: {len(self.selected_features)}\n")
            f.write(f"Model Type: XGBoost\n")
            f.write(f"Timestamp: {datetime.now().isoformat()}\n")
        
        print(f"  模型文件: {model_path}")
        print(f"  预处理器: {scaler_path}")
        print(f"  特征列表: {features_path}")
        print(f"  特征重要性: {importance_path}")
        print(f"  性能指标: {metrics_path}")
    
    def plot_results(self, results):
        """绘制结果图表"""
        print(f"\n📈 生成结果图表...")
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # ROC曲线
        fpr, tpr, _ = roc_curve(results['y_val_true'], results['y_val_proba'])
        axes[0, 0].plot(fpr, tpr, label=f'ROC (AUC = {results["val_auc"]:.3f})')
        axes[0, 0].plot([0, 1], [0, 1], 'k--')
        axes[0, 0].set_xlabel('False Positive Rate')
        axes[0, 0].set_ylabel('True Positive Rate')
        axes[0, 0].set_title('ROC Curve - Validation Set')
        axes[0, 0].legend()
        axes[0, 0].grid(True)
        
        # 验证集混淆矩阵
        sns.heatmap(results['val_cm'], annot=True, fmt='d', ax=axes[0, 1])
        axes[0, 1].set_title('Confusion Matrix - Validation Set')
        axes[0, 1].set_xlabel('Predicted')
        axes[0, 1].set_ylabel('Actual')
        
        # 特征重要性 (Top 15)
        top_features = results['feature_importance'].head(15)
        axes[1, 0].barh(range(len(top_features)), top_features['importance'])
        axes[1, 0].set_yticks(range(len(top_features)))
        axes[1, 0].set_yticklabels(top_features['feature'], fontsize=8)
        axes[1, 0].set_xlabel('Feature Importance')
        axes[1, 0].set_title('Top 15 Feature Importance')
        
        # 预测概率分布
        axes[1, 1].hist(results['y_val_proba'][results['y_val_true'] == 0], 
                       alpha=0.5, label='Normal', bins=20)
        axes[1, 1].hist(results['y_val_proba'][results['y_val_true'] == 1], 
                       alpha=0.5, label='Anomaly', bins=20)
        axes[1, 1].set_xlabel('Prediction Probability')
        axes[1, 1].set_ylabel('Count')
        axes[1, 1].set_title('Prediction Probability Distribution')
        axes[1, 1].legend()
        
        plt.tight_layout()
        
        # 保存图表
        plot_path = os.path.join(self.output_dir, 'model_results.png')
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"  结果图表: {plot_path}")
    
    def run_pipeline(self, feature_set="minimal", selection_method="statistical"):
        """运行完整的训练流水线"""
        print("🚀 启动TSfresh + XGBoost训练流水线...\n")
        
        # 1. 加载数据
        df = self.load_and_prepare_data()
        
        # 2. 提取特征
        X, y = self.extract_tsfresh_features(df, feature_set=feature_set)
        
        # 3. 特征选择
        X_selected, selected_features = self.feature_selection(X, y, selection_method)
        
        # 4. 训练模型
        X_train, X_val, y_train, y_val = self.train_model(X_selected, y)
        
        # 5. 评估模型
        results = self.evaluate_model(X_train, X_val, y_train, y_val)
        
        # 6. 保存结果
        self.save_model_and_results(results)
        
        # 7. 绘制图表
        self.plot_results(results)
        
        print(f"\n✅ 训练流水线完成！")
        print(f"   验证集AUC: {results['val_auc']:.4f}")
        print(f"   特征数量: {len(selected_features)}")
        print(f"   模型已保存到: {self.output_dir}")
        
        return results

# 主程序
if __name__ == "__main__":
    # 配置路径
    WINDOW_CSV_PATH = "windows/string_imbalance_windows.csv"
    OUTPUT_DIR = "models/string_imbalance"
    
    # 创建流水线
    pipeline = TSfreshXGBoostPipeline(
        window_csv_path=WINDOW_CSV_PATH,
        output_dir=OUTPUT_DIR
    )
    
    # 运行训练
    results = pipeline.run_pipeline(
        feature_set="minimal",      # minimal/efficient/full
        selection_method="statistical"  # statistical/none
    )
