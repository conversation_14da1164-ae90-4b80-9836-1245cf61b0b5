import pandas as pd
import os
import glob

# 1. 检查CSC_FAILURE原始数据
csc_file = 'data/anomaly_datasets/CSC_FAILURE.csv'
if os.path.exists(csc_file):
    csc_df = pd.read_csv(csc_file, encoding='utf-8-sig')
    print(f"CSC_FAILURE原始数据行数: {len(csc_df)}")
    
    # 检查唯一设备ID数量
    if 'stringid' in csc_df.columns:
        unique_devices = csc_df['stringid'].nunique()
        print(f"CSC_FAILURE中唯一设备ID数量: {unique_devices}")
        print(f"设备ID示例: {csc_df['stringid'].unique()[:5]}")
    
    # 检查时间戳范围
    if 'ts' in csc_df.columns:
        try:
            csc_df['ts'] = pd.to_datetime(csc_df['ts'])
            print(f"最早时间: {csc_df['ts'].min()}")
            print(f"最晚时间: {csc_df['ts'].max()}")
        except Exception as e:
            print(f"时间戳转换错误: {e}")

# 2. 检查其他数据集的时间和设备ID范围
print("\n检查其他数据集:")
other_files = [f for f in glob.glob('data/anomaly_datasets/*.csv') if 'CSC_FAILURE.csv' not in f]

for file in other_files[:3]:  # 只检查前3个文件作为示例
    try:
        df = pd.read_csv(file, encoding='utf-8-sig')
        file_name = os.path.basename(file)
        print(f"\n{file_name} 行数: {len(df)}")
        
        if 'stringid' in df.columns and 'ts' in df.columns:
            try:
                df['ts'] = pd.to_datetime(df['ts'])
                overlap = False
                
                # 检查是否与CSC_FAILURE有时间和设备ID重叠
                if 'stringid' in csc_df.columns and 'ts' in csc_df.columns:
                    for device in csc_df['stringid'].unique():
                        if device in df['stringid'].values:
                            csc_times = csc_df[csc_df['stringid'] == device]['ts']
                            other_times = df[df['stringid'] == device]['ts']
                            
                            # 检查时间重叠
                            for t in csc_times:
                                if t in other_times.values:
                                    print(f"发现重叠! 设备ID {device}, 时间 {t}")
                                    overlap = True
                                    break
                        if overlap:
                            break
                
                if not overlap:
                    print("未发现与CSC_FAILURE数据的重叠")
            except Exception as e:
                print(f"处理错误: {e}")
    except Exception as e:
        print(f"无法读取文件 {file}: {e}")

# 3. 检查合并过程中的CSC_FAILURE数据
# 模拟合并过程，但添加跟踪代码
print("\n模拟合并过程:")
dfs = []
for file in glob.glob('data/anomaly_datasets/*.csv')[:5]:  # 只使用前5个文件进行测试
    try:
        df = pd.read_csv(file, encoding='utf-8-sig')
        file_name = os.path.basename(file)
        
        # 清理标签
        if 'Label' in df.columns:
            df['Label'] = df['Label'].str.strip().str.replace(r'[\u200B-\u200D\uFEFF\u0000-\u001F\u007F-\u009F]', '', regex=True)
        
        # 记录每个文件的CSC_FAILURE数量
        if 'Label' in df.columns:
            csc_count = df[df['Label'] == 'CSC_FAILURE'].shape[0]
            print(f"{file_name} 中CSC_FAILURE数量: {csc_count}")
        
        dfs.append(df)
    except Exception as e:
        print(f"无法读取文件 {file}: {e}")

# 合并数据
if dfs:
    test_combined = pd.concat(dfs, ignore_index=True)
    print(f"测试合并后总行数: {len(test_combined)}")
    
    # 转换时间戳
    if 'ts' in test_combined.columns:
        test_combined['ts'] = pd.to_datetime(test_combined['ts'])
    
    # 检查重复
    if 'ts' in test_combined.columns and 'stringid' in test_combined.columns:
        duplicates = test_combined.duplicated(subset=['ts', 'stringid'], keep=False)
        dup_count = duplicates.sum()
        print(f"发现{dup_count}行重复数据")
        
        # 检查重复中包含CSC_FAILURE的数量
        if 'Label' in test_combined.columns:
            dup_with_csc = test_combined[duplicates & (test_combined['Label'] == 'CSC_FAILURE')].shape[0]
            print(f"重复数据中包含CSC_FAILURE的行数: {dup_with_csc}")