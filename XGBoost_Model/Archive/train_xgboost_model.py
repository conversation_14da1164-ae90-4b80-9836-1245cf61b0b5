import pandas as pd
import numpy as np
import os
import json
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import (
    accuracy_score,
    precision_recall_fscore_support,
    roc_auc_score,
    precision_recall_curve,
    confusion_matrix,
)
from sklearn.preprocessing import StandardScaler
import xgboost as xgb
import joblib
import time
import warnings

warnings.filterwarnings("ignore")

# ────────────────────────────────────────────────
# 0. Paths & Folders
# ────────────────────────────────────────────────
ROOT_DIR = "data/models_timebased"
PLOT_DIR = f"{ROOT_DIR}/plots"
RESULT_DIR = f"{ROOT_DIR}/results"
for d in (ROOT_DIR, PLOT_DIR, RESULT_DIR):
    os.makedirs(d, exist_ok=True)

print("▶ Starting time‑series XGBoost (class‑weighted) pipeline…")
start_ts = time.time()

# ────────────────────────────────────────────────
# 1. Load data
# ────────────────────────────────────────────────
features_df = pd.read_csv("data/model_ready/features.csv")
labels_df   = pd.read_csv("data/model_ready/labels.csv")
features_df["ts"] = pd.to_datetime(features_df["ts"])

# sort by time
features_df = features_df.sort_values("ts").reset_index(drop=True)
labels_df   = labels_df.iloc[features_df.index].reset_index(drop=True)

# ────────────────────────────────────────────────
# 2. Feature engineering
# ────────────────────────────────────────────────
features_df["sequence_id"] = range(1, len(features_df) + 1)
features_df["hour"]        = features_df["ts"].dt.hour
features_df["dayofweek"]   = features_df["ts"].dt.dayofweek
features_df["is_weekend"]  = features_df["dayofweek"] >= 5
features_df["month"]       = features_df["ts"].dt.month
features_df["day"]         = features_df["ts"].dt.day

# cyclical encodings
features_df["hour_sin"] = np.sin(2*np.pi*features_df["hour"] / 24)
features_df["hour_cos"] = np.cos(2*np.pi*features_df["hour"] / 24)
features_df["dow_sin"]  = np.sin(2*np.pi*features_df["dayofweek"] / 7)
features_df["dow_cos"]  = np.cos(2*np.pi*features_df["dayofweek"] / 7)

features_df["time_diff"] = features_df["ts"].diff().dt.total_seconds().div(60).fillna(0)

label_cols = [c for c in labels_df.columns if c != "id"]

# ────────────────────────────────────────────────
# 3. Time‑based train / test split
#    last 20 % → test
# ────────────────────────────────────────────────
cut = int(len(features_df)*0.8)
train_idx = range(cut)
test_idx  = range(cut, len(features_df))

exclude = {"ts", "softversion", "macid", "balancestatus", "mins", "localip"}
num_cols = [c for c in features_df.select_dtypes(["int64","float64"]).columns if c not in exclude]

X = features_df[num_cols]
y = labels_df[label_cols]

X_train, X_test = X.iloc[train_idx],  X.iloc[test_idx]
y_train, y_test = y.iloc[train_idx], y.iloc[test_idx]

# ────────────────────────────────────────────────
# 4. Scaling
# ────────────────────────────────────────────────
scaler = StandardScaler()
X_train_s = pd.DataFrame(scaler.fit_transform(X_train), index=X_train.index, columns=X_train.columns)
X_test_s  = pd.DataFrame(scaler.transform(X_test),   index=X_test.index,  columns=X_test.columns)

# save processed sets (optional)
X_train_s.to_csv(f"{ROOT_DIR}/X_train_scaled.csv")
X_test_s.to_csv (f"{ROOT_DIR}/X_test_scaled.csv")
y_train.to_csv(f"{ROOT_DIR}/y_train.csv")
y_test.to_csv (f"{ROOT_DIR}/y_test.csv")

# ────────────────────────────────────────────────
# 5. Validation split inside training window (last 10 % of train)
# ────────────────────────────────────────────────
val_cut = int(len(X_train_s)*0.9)
tr_idx  = X_train_s.index[:val_cut]
val_idx = X_train_s.index[val_cut:]

X_tr, X_val = X_train_s.loc[tr_idx], X_train_s.loc[val_idx]

# ────────────────────────────────────────────────
# 6. XGBoost param template
# ────────────────────────────────────────────────
base_params = dict(
    learning_rate = 0.1,
    max_depth     = 6,
    min_child_weight = 1,
    subsample     = 0.8,
    colsample_bytree = 0.8,
    objective     = "binary:logistic",
    n_estimators  = 200,
    eval_metric   = "logloss",
    random_state  = 42,
    use_label_encoder=False,
    base_score = 0.5,
)

models, thresholds = {}, {}
metrics_summary    = {}

print("▶ Training with per‑label class weights …")
for lbl in label_cols:
    y_tr  = y_train[lbl].loc[tr_idx]
    y_val = y_train[lbl].loc[val_idx]

    pos = y_tr.sum(); neg = len(y_tr) - pos
    # avoid division by zero
    weight = (neg/pos) if pos > 0 else 1
    params = {**base_params, "scale_pos_weight": weight}

    clf = xgb.XGBClassifier(**params)
    clf.fit(X_tr, y_tr)

    # ─ threshold tuning on validation set (max F1) ─
    proba_val = clf.predict_proba(X_val)[:,1]
    precision, recall, th = precision_recall_curve(y_val, proba_val)
    f1 = np.where((precision+recall)>0, 2*precision*recall/(precision+recall), 0)
    best_idx = f1.argmax()
    best_thr = th[best_idx] if best_idx < len(th) else 0.5

    # store
    models[lbl]      = clf
    thresholds[lbl]  = float(best_thr)

    # simple val metrics for log
    metrics_summary[lbl] = dict(
        val_f1      = float(f1[best_idx]),
        val_precision = float(precision[best_idx]),
        val_recall    = float(recall[best_idx]),
        thr           = float(best_thr),
        scale_weight  = float(weight),
    )
    print(f"  • {lbl:<25} w={weight:6.1f}  thr={best_thr:0.3f}  F1={f1[best_idx]:0.3f}")

# ────────────────────────────────────────────────
# 7. Test‑set evaluation using tuned thresholds
# ────────────────────────────────────────────────
print("▶ Evaluating on test set …")
proba_test = {lbl: mdl.predict_proba(X_test_s)[:,1] for lbl, mdl in models.items()}
Y_pred = pd.DataFrame({lbl: (proba_test[lbl] > thresholds[lbl]).astype(int) for lbl in label_cols}, index=X_test_s.index)

exact_acc = (Y_pred.values == y_test.values).all(axis=1).mean()
print(f"Exact‑match accuracy = {exact_acc:0.4f}")

results = {}
for lbl in label_cols:
    p,r,f,_ = precision_recall_fscore_support(y_test[lbl], Y_pred[lbl], average='binary', zero_division=0)
    auc = roc_auc_score(y_test[lbl], proba_test[lbl]) if len(np.unique(y_test[lbl]))>1 else np.nan
    results[lbl] = dict(precision=float(p), recall=float(r), f1=float(f), auc=float(auc))

# ────────────────────────────────────────────────
# 8. Persist artifacts
# ────────────────────────────────────────────────
for lbl, mdl in models.items():
    joblib.dump(mdl, f"{ROOT_DIR}/xgb_{lbl}.joblib")

pd.DataFrame(results).T.to_csv(f"{RESULT_DIR}/label_metrics.csv")
pd.Series(thresholds).to_json(f"{RESULT_DIR}/thresholds.json")

# ────────────────────────────────────────────────
# 9. Quick visual (F1 barplot) – optional
# ────────────────────────────────────────────────
plt.figure(figsize=(10,5))
bar_df = pd.DataFrame(results).T.sort_values("f1")
sns.barplot(x=bar_df.index, y="f1", data=bar_df)
plt.xticks(rotation=45, ha='right')
plt.title("Test‑set F1 by label (weighted + tuned)")
plt.tight_layout()
plt.savefig(f"{PLOT_DIR}/f1_scores_weighted.png")
plt.close()

# ────────────────────────────────────────────────
# 10. Summary log
# ────────────────────────────────────────────────
print("\n=== Validation summary (per label) ===")
for lbl, m in metrics_summary.items():
    print(f"{lbl:<25} F1={m['val_f1']:.3f}  prec={m['val_precision']:.3f}  rec={m['val_recall']:.3f}  thr={m['thr']:.3f}")

print("\nArtifacts saved to:")
print(f"  Models   → {ROOT_DIR}/*.joblib")
print(f"  Metrics  → {RESULT_DIR}/label_metrics.csv")
print(f"  Thresh   → {RESULT_DIR}/thresholds.json")
print(f"  Plots    → {PLOT_DIR}/f1_scores_weighted.png")
print(f"Execution time: {time.time()-start_ts:0.1f}s")
