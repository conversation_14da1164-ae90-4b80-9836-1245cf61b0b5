"""fetch_string_imbalance.py

Query 30‑minute windows for the device 8C‑1F‑64‑C5‑12‑52, generate
positive/negative windows for the STRING_IMBALANCE anomaly and export
as CSV ready for tsfresh + modelling.

Usage:
    python fetch_string_imbalance.py --start 2024-01-01 --end 2024-01-31 \
           --dbname tsdb --host ol7985ua81.b49qsab0at.tsdb.cloud.timescale.com \
           --port 31277 --user tsdbadmin --password rpltd7b7w9gnka4s

Notes:
- DB credentials can alternatively be supplied via environment vars:
  DB_HOST, DB_NAME, DB_USER, DB_PASS, DB_PORT.
- No data is written back to DB; all outputs are local CSV files.
"""

import os
import sys
import argparse
import csv
from datetime import datetime, timedelta

import psycopg2               # `pip install psycopg2-binary`
import pandas as pd
import numpy as np
from tqdm import tqdm

# ────────────────────────────────
# args / env
# ────────────────────────────────
parser = argparse.ArgumentParser()
parser.add_argument('--host',     default=os.getenv('DB_HOST', 'ol7985ua81.b49qsab0at.tsdb.cloud.timescale.com'))
parser.add_argument('--port',     default=os.getenv('DB_PORT', '31277'))
parser.add_argument('--dbname',   default=os.getenv('DB_NAME', 'tsdb'))
parser.add_argument('--user',     default=os.getenv('DB_USER', 'tsdbadmin'))
parser.add_argument('--password', default=os.getenv('DB_PASS', 'rpltd7b7w9gnka4s'))
parser.add_argument('--start',    default='2024-01-01', help='YYYY-MM-DD')
parser.add_argument('--end',      default='2025-05-01', help='YYYY-MM-DD')
parser.add_argument('--device',   default='8C-1F-64-C5-12-52')
parser.add_argument('--out',      default='windows/string_imbalance_windows.csv')
parser.add_argument('--pos_per_label', type=int, default=1)
parser.add_argument('--neg_ratio', type=int, default=3)
args = parser.parse_args()

os.makedirs(os.path.dirname(args.out), exist_ok=True)

# ────────────────────────────────
# connect db & stream query
# ────────────────────────────────
conn = psycopg2.connect(
    host=args.host,
    port=args.port,
    dbname=args.dbname,
    user=args.user,
    password=args.password,
    options='-c search_path=public'
)

SQL = (
    "SELECT * FROM ess_string_hon "
    "WHERE macid = %s AND ts BETWEEN %s AND %s "
    "ORDER BY ts"
)

with conn.cursor() as cur:
    cur.execute(SQL, (args.device, args.start, args.end))
    columns = [desc[0] for desc in cur.description]
    chunks = []
    while True:
        rows = cur.fetchmany(10000)
        if not rows:
            break
        df_chunk = pd.DataFrame(rows, columns=columns)
        chunks.append(df_chunk)

raw_df = pd.concat(chunks).reset_index(drop=True)
raw_df['ts'] = pd.to_datetime(raw_df['ts'])

if raw_df.empty:
    print('No data returned for given range.'); sys.exit(0)

# ────────────────────────────────
# derive anomaly flag
# ────────────────────────────────
raw_df['is_imb'] = (raw_df['sysstatus'] == 4)

# ────────────────────────────────
# sliding window generation
# ────────────────────────────────
L, S = 30, 5   # window length & stride (rows)
windows = []
start_idx = 0

# 新增计数器
pos_count, neg_count = 0, 0
pos_target = args.pos_per_label
neg_target = args.pos_per_label * args.neg_ratio

while start_idx + L <= len(raw_df):
    window = raw_df.iloc[start_idx:start_idx+L]
    # 连续性检查
    if (window['ts'].diff().dropna() == pd.Timedelta(minutes=1)).all():
        pos_flag = window['is_imb'].any()
        label = int(pos_flag)
        if label == 1 and pos_count < pos_target:
            windows.append((label, window))
            pos_count += 1
        elif label == 0 and neg_count < neg_target:
        windows.append((label, window))
            neg_count += 1
        # 满足条件立即退出
        if pos_count >= pos_target and neg_count >= neg_target:
            break
    start_idx += S

# ────────────────────────────────
# 采样正/负窗口
# ────────────────────────────────
pos_windows = [w for w in windows if w[0]==1]
neg_windows = [w for w in windows if w[0]==0]

# 限制正例数量
if len(pos_windows) > args.pos_per_label:
    pos_windows = pos_windows[:args.pos_per_label]

neg_target = len(pos_windows)*args.neg_ratio
neg_windows = neg_windows[:neg_target]

print(f"Selected {len(pos_windows)} positive & {len(neg_windows)} normal windows")

# ────────────────────────────────
# flatten & export
# ────────────────────────────────
rows_to_write = []
window_id = 0
for label, win in pos_windows + neg_windows:
    window_id += 1
    ts_start, ts_end = win['ts'].iloc[0], win['ts'].iloc[-1]
    # 计算窗口平均/最大等简单统计，直接保存；tsfresh 将在后续脚本做
    feature_vals = win.drop(columns=['ts','macid','is_imb']).mean()  # 简例
    row = {
        'window_id': window_id,
        'ts_start': ts_start,
        'ts_end': ts_end,
        **feature_vals.to_dict(),
        'string_imbalance': label
    }
    rows_to_write.append(row)

pd.DataFrame(rows_to_write).to_csv(args.out, index=False)
print(f"Written windows → {args.out}")
