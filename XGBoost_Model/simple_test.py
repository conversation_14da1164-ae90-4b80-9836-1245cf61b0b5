#!/usr/bin/env python3
"""
简化的模型测试脚本
"""

import pandas as pd
import numpy as np
import pickle
import json

def main():
    print("🧪 简化模型测试...")
    
    # 1. 加载模型
    print("📁 加载模型...")
    with open('models/xgboost_model.pkl', 'rb') as f:
        model = pickle.load(f)
    
    with open('models/threshold_config.json', 'r') as f:
        threshold_config = json.load(f)
    
    print(f"   模型加载成功，阈值: {threshold_config['best_threshold']}")
    
    # 2. 加载测试数据
    print("📊 加载测试数据...")
    test_data = pd.read_csv('data/processed_data/test_raw_data.csv')
    print(f"   测试数据: {test_data.shape}")
    
    # 转换异常标签
    test_data['anomaly'] = test_data['is_string_imbalance'].map({'True': 1, 'False': 0, True: 1, False: 0})
    anomaly_count = test_data['anomaly'].sum()
    print(f"   异常数据点: {anomaly_count}/{len(test_data)} ({anomaly_count/len(test_data)*100:.1f}%)")
    
    # 3. 创建时间窗口（与训练时保持一致：30行为一个窗口）
    print("🪟 创建时间窗口...")
    test_data['ts'] = pd.to_datetime(test_data['ts'])
    test_data = test_data.sort_values('ts').reset_index(drop=True)

    # 按照训练时的方法：每30行为一个窗口
    window_size = 30
    test_data['window_id'] = test_data.index // window_size + 1

    # 移除不完整的最后一个窗口（如果不足30行）
    complete_windows = (len(test_data) // window_size) * window_size
    test_data = test_data.iloc[:complete_windows].copy()
    test_data['window_id'] = test_data.index // window_size + 1

    n_windows = test_data['window_id'].max()
    print(f"   创建了 {n_windows} 个完整窗口（每窗口30行）")
    print(f"   使用数据点: {len(test_data)}/{len(test_data) + (len(test_data) % window_size)}")
    
    # 4. 提取特征
    print("🔧 提取特征...")
    CORE_FEATURES = ['cellvdelta', 'averagecellv', 'hcellv', 'lcellv', 'systemvolt', 'totalcurrenta', 'soc', 'htempc', 'ltempc']
    
    # 聚合原始特征
    test_agg = test_data.groupby('window_id')[CORE_FEATURES].agg(['mean', 'std', 'min', 'max', 'median']).round(6)
    test_agg.columns = ['_'.join(col).strip() for col in test_agg.columns.values]
    
    # 创建简化的TSFresh特征
    tsfresh_features = pd.DataFrame()
    for col in CORE_FEATURES:
        tsfresh_features[f'{col}__maximum'] = test_data.groupby('window_id')[col].max().values
        tsfresh_features[f'{col}__absolute_maximum'] = test_data.groupby('window_id')[col].max().abs().values
        tsfresh_features[f'{col}__mean'] = test_data.groupby('window_id')[col].mean().values
        tsfresh_features[f'{col}__standard_deviation'] = test_data.groupby('window_id')[col].std().fillna(0).values
        tsfresh_features[f'{col}__median'] = test_data.groupby('window_id')[col].median().values
        tsfresh_features[f'{col}__minimum'] = test_data.groupby('window_id')[col].min().values
        tsfresh_features[f'{col}__variance'] = test_data.groupby('window_id')[col].var().fillna(0).values
        tsfresh_features[f'{col}__sum_values'] = test_data.groupby('window_id')[col].sum().values
        tsfresh_features[f'{col}__root_mean_square'] = np.sqrt(test_data.groupby('window_id')[col].apply(lambda x: (x**2).mean())).fillna(0).values
    
    # 合并特征
    test_agg.reset_index(drop=True, inplace=True)
    X_test = pd.concat([test_agg, tsfresh_features], axis=1)
    
    # 创建标签（与训练时保持一致：窗口中有任何异常则标记为异常）
    window_labels = test_data.groupby('window_id')['anomaly'].max().reset_index()
    y_test = window_labels['anomaly'].values

    print(f"   窗口标签分布:")
    print(f"     正常窗口: {np.sum(y_test==0)}")
    print(f"     异常窗口: {np.sum(y_test==1)}")
    print(f"     窗口异常率: {np.mean(y_test)*100:.1f}%")
    
    print(f"   最终特征形状: {X_test.shape}")
    print(f"   准备进行预测...")
    
    # 5. 进行预测
    print("🔮 模型预测...")
    y_pred_proba = model.predict_proba(X_test)[:, 1]
    y_pred = (y_pred_proba >= threshold_config['best_threshold']).astype(int)
    
    print(f"   预测概率范围: {y_pred_proba.min():.4f} - {y_pred_proba.max():.4f}")
    print(f"   预测异常窗口: {y_pred.sum()}/{len(y_pred)}")
    
    # 6. 评估性能
    print("📊 性能评估...")
    from sklearn.metrics import classification_report, confusion_matrix, f1_score, precision_score, recall_score, roc_auc_score
    
    f1 = f1_score(y_test, y_pred)
    precision = precision_score(y_test, y_pred, zero_division=0)
    recall = recall_score(y_test, y_pred, zero_division=0)
    auc = roc_auc_score(y_test, y_pred_proba)
    
    print(f"\n🎯 测试结果:")
    print(f"   F1-Score: {f1:.3f}")
    print(f"   Precision: {precision:.3f}")
    print(f"   Recall: {recall:.3f}")
    print(f"   AUC-ROC: {auc:.3f}")
    
    # 混淆矩阵
    cm = confusion_matrix(y_test, y_pred)
    print(f"\n   混淆矩阵:")
    print(f"   TN: {cm[0,0]:2d} | FP: {cm[0,1]:2d}")
    print(f"   FN: {cm[1,0]:2d} | TP: {cm[1,1]:2d}")
    
    # 详细报告
    print(f"\n   详细分类报告:")
    print(classification_report(y_test, y_pred, target_names=['正常', '异常']))
    
    # 保存结果
    results = {
        'test_windows': len(y_test),
        'test_anomaly_rate': float(y_test.mean()),
        'predicted_anomaly_rate': float(y_pred.mean()),
        'f1_score': float(f1),
        'precision': float(precision),
        'recall': float(recall),
        'auc_roc': float(auc),
        'confusion_matrix': cm.tolist()
    }
    
    with open('results/simple_test_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n💾 结果已保存到: results/simple_test_results.json")
    print("✅ 测试完成！")

if __name__ == "__main__":
    main()
