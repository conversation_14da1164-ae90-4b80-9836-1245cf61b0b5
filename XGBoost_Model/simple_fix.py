#!/usr/bin/env python3
"""
简单的数据划分修复脚本
"""

import pandas as pd
import numpy as np
import os

def main():
    print("🔧 开始修复数据划分...")
    
    # 1. 创建备份目录
    backup_dir = 'data/processed_data/backup'
    os.makedirs(backup_dir, exist_ok=True)
    print(f"📁 备份目录: {backup_dir}")
    
    # 2. 备份原文件
    files_to_backup = [
        'train_feature_labels.csv', 
        'val_feature_labels.csv'
    ]
    
    for file in files_to_backup:
        src = f'data/processed_data/{file}'
        dst = f'{backup_dir}/{file}'
        if os.path.exists(src):
            import shutil
            shutil.copy2(src, dst)
            print(f"   备份: {file}")
    
    # 3. 加载标签数据
    print("📊 加载标签数据...")
    train_labels = pd.read_csv('data/processed_data/train_feature_labels.csv')
    val_labels = pd.read_csv('data/processed_data/val_feature_labels.csv')
    
    print(f"   训练集: {len(train_labels)} 样本")
    print(f"   验证集: {len(val_labels)} 样本")
    
    # 4. 分析当前分布
    print("🔍 分析当前分布...")
    print(f"   训练集异常率: {train_labels['label'].mean()*100:.1f}%")
    print(f"   验证集异常率: {val_labels['label'].mean()*100:.1f}%")
    
    # 5. 合并所有数据
    all_labels = pd.concat([train_labels, val_labels], ignore_index=True)
    print(f"   总样本: {len(all_labels)}")
    print(f"   总异常率: {all_labels['label'].mean()*100:.1f}%")
    
    # 6. 创建随机索引
    print("🎲 创建随机划分...")
    np.random.seed(42)
    
    # 分别处理异常和正常样本
    anomaly_indices = all_labels[all_labels['label'] == 1].index.tolist()
    normal_indices = all_labels[all_labels['label'] == 0].index.tolist()
    
    print(f"   异常样本: {len(anomaly_indices)}")
    print(f"   正常样本: {len(normal_indices)}")
    
    # 随机打乱
    np.random.shuffle(anomaly_indices)
    np.random.shuffle(normal_indices)
    
    # 按比例划分 (80% 训练, 20% 验证)
    anomaly_train_size = int(len(anomaly_indices) * 0.8)
    normal_train_size = int(len(normal_indices) * 0.8)
    
    train_indices = (anomaly_indices[:anomaly_train_size] + 
                    normal_indices[:normal_train_size])
    val_indices = (anomaly_indices[anomaly_train_size:] + 
                  normal_indices[normal_train_size:])
    
    # 7. 生成新的标签文件
    print("💾 生成新的标签文件...")
    new_train_labels = all_labels.iloc[train_indices].copy()
    new_val_labels = all_labels.iloc[val_indices].copy()
    
    # 重新分配window_id (保持连续)
    new_train_labels = new_train_labels.reset_index(drop=True)
    new_val_labels = new_val_labels.reset_index(drop=True)
    
    # 保存新文件
    new_train_labels.to_csv('data/processed_data/train_feature_labels_new.csv', index=False)
    new_val_labels.to_csv('data/processed_data/val_feature_labels_new.csv', index=False)
    
    # 8. 验证结果
    print("✅ 验证修复结果...")
    print(f"   新训练集: {len(new_train_labels)} 样本")
    print(f"     异常: {(new_train_labels['label'] == 1).sum()}")
    print(f"     正常: {(new_train_labels['label'] == 0).sum()}")
    print(f"     异常率: {new_train_labels['label'].mean()*100:.1f}%")
    
    print(f"   新验证集: {len(new_val_labels)} 样本")
    print(f"     异常: {(new_val_labels['label'] == 1).sum()}")
    print(f"     正常: {(new_val_labels['label'] == 0).sum()}")
    print(f"     异常率: {new_val_labels['label'].mean()*100:.1f}%")
    
    print("\n🎉 标签文件修复完成!")
    print("新文件已保存为 *_new.csv")
    print("请检查结果后手动替换原文件。")

if __name__ == "__main__":
    main()
