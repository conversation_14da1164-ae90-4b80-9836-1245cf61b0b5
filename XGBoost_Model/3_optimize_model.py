#!/usr/bin/env python3
"""
模型优化脚本 - 特征选择和阈值优化
"""

import pandas as pd
import numpy as np
import pickle
from sklearn.metrics import classification_report, confusion_matrix, f1_score, precision_score, recall_score, roc_auc_score
import matplotlib.pyplot as plt
import seaborn as sns

def load_data_and_model():
    """加载数据和模型"""
    print("📁 加载数据和模型...")
    
    # 加载模型
    with open('models/xgboost_model.pkl', 'rb') as f:
        model = pickle.load(f)
    
    # 重建验证集数据（与训练脚本保持一致）
    val_windows = pd.read_csv('data/processed_data/val_windows.csv')
    val_tsfresh = pd.read_csv('data/processed_data/val_tsfresh_features.csv', index_col=0)
    val_labels = pd.read_csv('data/processed_data/val_feature_labels.csv')
    
    # 聚合原始特征
    CORE_FEATURES = ['cellvdelta', 'averagecellv', 'hcellv', 'lcellv', 'systemvolt', 'totalcurrenta', 'soc', 'htempc', 'ltempc']
    val_agg = val_windows.groupby('window_id')[CORE_FEATURES].agg(['mean', 'std', 'min', 'max', 'median']).round(6)
    val_agg.columns = ['_'.join(col).strip() for col in val_agg.columns.values]
    val_agg.reset_index(drop=True, inplace=True)
    val_tsfresh.reset_index(drop=True, inplace=True)
    
    X_val = pd.concat([val_agg, val_tsfresh], axis=1)
    y_val = val_labels['label'].values
    
    return model, X_val, y_val

def feature_selection_optimization(model, X_val, y_val):
    """特征选择优化"""
    print("\n🔍 特征选择优化...")
    
    # 加载特征重要性
    feature_importance = pd.read_csv('results/feature_importance.csv')
    
    # 统计零重要性特征
    zero_importance = feature_importance[feature_importance['importance'] == 0]
    print(f"   零重要性特征数量: {len(zero_importance)}/{len(feature_importance)}")
    
    # 尝试不同的特征选择阈值
    thresholds = [0.001, 0.005, 0.01, 0.02]
    results = []
    
    print("   测试不同特征选择阈值:")
    print("   阈值     特征数   F1     Precision  Recall")
    print("   " + "-" * 45)
    
    for threshold in thresholds:
        # 选择重要性大于阈值的特征
        selected_features = feature_importance[feature_importance['importance'] >= threshold]['feature'].tolist()
        
        if len(selected_features) == 0:
            continue
            
        # 使用选择的特征进行预测
        X_val_selected = X_val[selected_features]
        y_pred_proba = model.predict_proba(X_val_selected)[:, 1]
        y_pred = (y_pred_proba >= 0.1).astype(int)
        
        # 计算性能指标
        f1 = f1_score(y_val, y_pred)
        precision = precision_score(y_val, y_pred, zero_division=0)
        recall = recall_score(y_val, y_pred, zero_division=0)
        
        results.append({
            'threshold': threshold,
            'n_features': len(selected_features),
            'f1': f1,
            'precision': precision,
            'recall': recall,
            'features': selected_features
        })
        
        print(f"   {threshold:.3f}    {len(selected_features):3d}     {f1:.3f}  {precision:.3f}     {recall:.3f}")
    
    # 找到最佳特征选择
    if results:
        best_result = max(results, key=lambda x: x['f1'])
        print(f"\n   最佳特征选择:")
        print(f"   阈值: {best_result['threshold']}")
        print(f"   特征数: {best_result['n_features']}")
        print(f"   F1: {best_result['f1']:.3f}")
        print(f"   Precision: {best_result['precision']:.3f}")
        
        return best_result['features']
    
    return X_val.columns.tolist()

def threshold_optimization(model, X_val, y_val, selected_features=None):
    """阈值优化"""
    print("\n🎯 精细阈值优化...")
    
    if selected_features:
        X_val_use = X_val[selected_features]
    else:
        X_val_use = X_val
    
    y_pred_proba = model.predict_proba(X_val_use)[:, 1]
    
    # 更精细的阈值搜索
    thresholds = np.arange(0.05, 0.95, 0.05)
    results = []
    
    print("   阈值    F1     Precision  Recall   平衡分数")
    print("   " + "-" * 45)
    
    for threshold in thresholds:
        y_pred = (y_pred_proba >= threshold).astype(int)
        
        f1 = f1_score(y_val, y_pred, zero_division=0)
        precision = precision_score(y_val, y_pred, zero_division=0)
        recall = recall_score(y_val, y_pred, zero_division=0)
        
        # 平衡分数：优先考虑precision，但不能牺牲太多recall
        balance_score = 0.6 * precision + 0.4 * recall if recall >= 0.8 else 0.3 * precision + 0.7 * recall
        
        results.append({
            'threshold': threshold,
            'f1': f1,
            'precision': precision,
            'recall': recall,
            'balance_score': balance_score
        })
        
        print(f"   {threshold:.2f}    {f1:.3f}  {precision:.3f}     {recall:.3f}    {balance_score:.3f}")
    
    # 找到最佳阈值
    best_threshold = max(results, key=lambda x: x['balance_score'])
    print(f"\n   推荐阈值: {best_threshold['threshold']:.2f}")
    print(f"   对应性能: F1={best_threshold['f1']:.3f}, Precision={best_threshold['precision']:.3f}, Recall={best_threshold['recall']:.3f}")
    
    return best_threshold['threshold']

def evaluate_optimized_model(model, X_val, y_val, selected_features, best_threshold):
    """评估优化后的模型"""
    print("\n📊 优化后模型评估...")
    
    X_val_optimized = X_val[selected_features]
    y_pred_proba = model.predict_proba(X_val_optimized)[:, 1]
    y_pred = (y_pred_proba >= best_threshold).astype(int)
    
    # 计算性能指标
    f1 = f1_score(y_val, y_pred)
    precision = precision_score(y_val, y_pred, zero_division=0)
    recall = recall_score(y_val, y_pred, zero_division=0)
    auc = roc_auc_score(y_val, y_pred_proba)
    
    print(f"   特征数量: {len(selected_features)} (原来114个)")
    print(f"   最佳阈值: {best_threshold:.2f}")
    print(f"   F1-Score: {f1:.3f}")
    print(f"   Precision: {precision:.3f}")
    print(f"   Recall: {recall:.3f}")
    print(f"   AUC-ROC: {auc:.3f}")
    
    # 混淆矩阵
    cm = confusion_matrix(y_val, y_pred)
    print(f"\n   混淆矩阵:")
    print(f"   TN: {cm[0,0]:2d} | FP: {cm[0,1]:2d}")
    print(f"   FN: {cm[1,0]:2d} | TP: {cm[1,1]:2d}")
    
    # 保存优化配置
    optimization_config = {
        'selected_features': selected_features,
        'best_threshold': best_threshold,
        'performance': {
            'f1_score': f1,
            'precision': precision,
            'recall': recall,
            'auc_roc': auc
        }
    }
    
    with open('models/optimization_config.json', 'w') as f:
        import json
        json.dump(optimization_config, f, indent=2)
    
    print(f"\n   优化配置已保存: models/optimization_config.json")
    
    return optimization_config

def main():
    print("🚀 模型优化开始...")
    
    # 1. 加载数据和模型
    model, X_val, y_val = load_data_and_model()
    print(f"   验证集: {X_val.shape}, 标签: {len(y_val)}")
    
    # 2. 特征选择优化
    selected_features = feature_selection_optimization(model, X_val, y_val)
    
    # 3. 阈值优化
    best_threshold = threshold_optimization(model, X_val, y_val, selected_features)
    
    # 4. 评估优化后的模型
    optimization_config = evaluate_optimized_model(model, X_val, y_val, selected_features, best_threshold)
    
    print("\n🎉 模型优化完成!")
    print(f"   特征数量减少: {114 - len(selected_features)}个")
    print(f"   推荐阈值: {best_threshold:.2f}")
    print(f"   优化后Precision: {optimization_config['performance']['precision']:.3f}")

if __name__ == "__main__":
    main()
