#!/usr/bin/env python3
"""
根据新的标签文件重新组织所有数据
"""

import pandas as pd
import os

def reorganize_data():
    print("🔄 重新组织数据文件...")
    
    # 1. 加载新的标签文件
    print("📊 加载新的标签文件...")
    train_labels_new = pd.read_csv('data/processed_data/train_feature_labels_fixed.csv')
    val_labels_new = pd.read_csv('data/processed_data/val_feature_labels_fixed.csv')
    
    print(f"   新训练集: {len(train_labels_new)} 窗口")
    print(f"   新验证集: {len(val_labels_new)} 窗口")
    
    # 2. 加载原始的所有数据
    print("📁 加载原始数据...")
    
    # 加载窗口数据
    train_windows_orig = pd.read_csv('data/processed_data/train_windows.csv')
    val_windows_orig = pd.read_csv('data/processed_data/val_windows.csv')
    all_windows = pd.concat([train_windows_orig, val_windows_orig], ignore_index=True)
    
    # 加载TSFresh特征
    train_tsfresh_orig = pd.read_csv('data/processed_data/train_tsfresh_features.csv', index_col=0)
    val_tsfresh_orig = pd.read_csv('data/processed_data/val_tsfresh_features.csv', index_col=0)
    
    # 重置索引并合并
    train_tsfresh_orig.reset_index(drop=True, inplace=True)
    val_tsfresh_orig.reset_index(drop=True, inplace=True)
    all_tsfresh = pd.concat([train_tsfresh_orig, val_tsfresh_orig], ignore_index=True)
    
    # 加载原始标签（用于映射）
    train_labels_orig = pd.read_csv('data/processed_data/train_feature_labels.csv')
    val_labels_orig = pd.read_csv('data/processed_data/val_feature_labels.csv')
    all_labels_orig = pd.concat([train_labels_orig, val_labels_orig], ignore_index=True)
    
    print(f"   合并后窗口数据: {all_windows.shape}")
    print(f"   合并后TSFresh特征: {all_tsfresh.shape}")
    print(f"   合并后标签: {len(all_labels_orig)}")
    
    # 3. 创建窗口ID到索引的映射
    print("🗺️  创建窗口ID映射...")
    window_to_index = {}
    for idx, window_id in enumerate(all_labels_orig['window_id']):
        window_to_index[window_id] = idx
    
    # 4. 根据新标签重新组织训练集
    print("🔄 重新组织训练集...")
    train_indices = []
    for _, row in train_labels_new.iterrows():
        window_id = row['window_id']
        if window_id in window_to_index:
            train_indices.append(window_to_index[window_id])
        else:
            print(f"   警告: 找不到窗口ID {window_id}")
    
    # 生成新的训练集数据
    new_train_windows = all_windows[all_windows['window_id'].isin(train_labels_new['window_id'])].copy()
    new_train_tsfresh = all_tsfresh.iloc[train_indices].reset_index(drop=True)
    
    # 5. 根据新标签重新组织验证集
    print("🔄 重新组织验证集...")
    val_indices = []
    for _, row in val_labels_new.iterrows():
        window_id = row['window_id']
        if window_id in window_to_index:
            val_indices.append(window_to_index[window_id])
        else:
            print(f"   警告: 找不到窗口ID {window_id}")
    
    # 生成新的验证集数据
    new_val_windows = all_windows[all_windows['window_id'].isin(val_labels_new['window_id'])].copy()
    new_val_tsfresh = all_tsfresh.iloc[val_indices].reset_index(drop=True)
    
    # 6. 备份原文件
    print("💾 备份原文件...")
    backup_dir = 'data/processed_data/backup_original'
    os.makedirs(backup_dir, exist_ok=True)
    
    files_to_backup = [
        'train_windows.csv', 'val_windows.csv',
        'train_tsfresh_features.csv', 'val_tsfresh_features.csv',
        'train_feature_labels.csv', 'val_feature_labels.csv'
    ]
    
    for file in files_to_backup:
        src = f'data/processed_data/{file}'
        dst = f'{backup_dir}/{file}'
        if os.path.exists(src):
            import shutil
            shutil.copy2(src, dst)
    
    print(f"   原文件已备份到: {backup_dir}")
    
    # 7. 保存新的数据文件
    print("💾 保存新的数据文件...")
    
    new_train_windows.to_csv('data/processed_data/train_windows.csv', index=False)
    new_val_windows.to_csv('data/processed_data/val_windows.csv', index=False)
    
    new_train_tsfresh.to_csv('data/processed_data/train_tsfresh_features.csv')
    new_val_tsfresh.to_csv('data/processed_data/val_tsfresh_features.csv')
    
    train_labels_new.to_csv('data/processed_data/train_feature_labels.csv', index=False)
    val_labels_new.to_csv('data/processed_data/val_feature_labels.csv', index=False)
    
    # 8. 验证结果
    print("✅ 验证重组结果...")
    print(f"   新训练集窗口数据: {new_train_windows.shape}")
    print(f"   新训练集TSFresh: {new_train_tsfresh.shape}")
    print(f"   新训练集标签: {len(train_labels_new)}")
    print(f"   训练集异常率: {train_labels_new['label'].mean()*100:.1f}%")
    
    print(f"   新验证集窗口数据: {new_val_windows.shape}")
    print(f"   新验证集TSFresh: {new_val_tsfresh.shape}")
    print(f"   新验证集标签: {len(val_labels_new)}")
    print(f"   验证集异常率: {val_labels_new['label'].mean()*100:.1f}%")
    
    print("\n🎉 数据重组完成!")
    print("现在可以重新训练模型了，应该会得到更好的Precision!")

if __name__ == "__main__":
    reorganize_data()
